import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import FormSection from "../components/design-system/form-section";
declare const meta: Meta<typeof FormSection>;
export default meta;
type Story = StoryObj<typeof FormSection>;
export declare const Default: Story;
export declare const WithAddNew: Story;
export declare const WithFormContent: Story;
//# sourceMappingURL=form-section.stories.d.ts.map