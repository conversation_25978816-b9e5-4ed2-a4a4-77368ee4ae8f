import React from "react";

interface DataFilesProps {
  color?: string;
  size?: number;
  className?: string;
}

const DataFiles = ({
  color = "white",
  size = 18,
  className,
}: DataFilesProps): React.JSX.Element => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M16.5 12.555V3.50252C16.5 2.60252 15.765 1.93502 14.8725 2.01002H14.8275C13.2525 2.14502 10.86 2.94752 9.525 3.78752L9.3975 3.87002C9.18 4.00502 8.82 4.00502 8.6025 3.87002L8.415 3.75752C7.08 2.92502 4.695 2.13002 3.12 2.00252C2.2275 1.92752 1.5 2.60252 1.5 3.49502V12.555C1.5 13.275 2.085 13.95 2.805 14.04L3.0225 14.07C4.65 14.2875 7.1625 15.1125 8.6025 15.9L8.6325 15.915C8.835 16.0275 9.1575 16.0275 9.3525 15.915C10.7925 15.12 13.3125 14.2875 14.9475 14.07L15.195 14.04C15.915 13.95 16.5 13.275 16.5 12.555Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9 4.11743V15.3674"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.8125 6.36743H4.125"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.375 8.61743H4.125"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default DataFiles;
