import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import SearchField from "../components/design-system/search-field";
declare const meta: Meta<typeof SearchField>;
export default meta;
type Story = StoryObj<typeof SearchField>;
export declare const Default: Story;
export declare const WithValue: Story;
export declare const Required: Story;
export declare const Disabled: Story;
export declare const AutoFocus: Story;
//# sourceMappingURL=search-field.stories.d.ts.map