import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { GroupedDropdown } from "../components/design-system/grouped-dropdown";

const meta: Meta<typeof GroupedDropdown> = {
  title: "Design System/GroupedDropdown",
  component: GroupedDropdown,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: { control: "text" },
    options: { control: "object" },
    selected: { control: "object" },
    onChange: { action: "changed" },
    placeholder: { control: "text" },
    maxDisplayItems: { control: "number" },
    required: { control: "boolean" },
  },
};

export default meta;
type Story = StoryObj<typeof GroupedDropdown>;

const sampleOptions = [
  {
    value: "group1",
    label: "Group 1",
    items: [
      { value: "1-1", label: "Item 1-1" },
      { value: "1-2", label: "Item 1-2" },
      { value: "1-3", label: "Item 1-3" },
    ],
  },
  {
    value: "group2",
    label: "Group 2",
    items: [
      { value: "2-1", label: "Item 2-1" },
      { value: "2-2", label: "Item 2-2" },
      { value: "2-3", label: "Item 2-3" },
    ],
  },
  {
    value: "group3",
    label: "Group 3",
    items: [
      { value: "3-1", label: "Item 3-1" },
      { value: "3-2", label: "Item 3-2" },
      { value: "3-3", label: "Item 3-3" },
    ],
  },
];

export const Default: Story = {
  args: {
    label: "Select Options",
    options: sampleOptions,
    selected: [],
    placeholder: "Select items",
    maxDisplayItems: 3,
    required: false,
  },
};

export const WithSelectedItems: Story = {
  args: {
    label: "Select Options",
    options: sampleOptions,
    selected: ["1-1", "2-2"],
    placeholder: "Select items",
    maxDisplayItems: 3,
    required: false,
  },
};

export const Required: Story = {
  args: {
    label: "Select Options",
    options: sampleOptions,
    selected: [],
    placeholder: "Select items",
    maxDisplayItems: 3,
    required: true,
  },
};

export const MultipleSelected: Story = {
  args: {
    label: "Select Options",
    options: sampleOptions,
    selected: ["1-1", "1-2", "2-1", "3-1"],
    placeholder: "Select items",
    maxDisplayItems: 3,
    required: false,
  },
};

export const CustomMaxDisplayItems: Story = {
  args: {
    label: "Select Options",
    options: sampleOptions,
    selected: ["1-1", "1-2", "2-1", "3-1"],
    placeholder: "Select items",
    maxDisplayItems: 2,
    required: false,
  },
}; 