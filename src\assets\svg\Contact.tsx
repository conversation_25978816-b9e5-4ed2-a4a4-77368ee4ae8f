import React from "react";

interface ContactProps {
  className?: string;
  color?: string;
  size?: number;
}

const Contact = ({
  className,
  color = "white",
  size = 18,
}: ContactProps): React.JSX.Element => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M12.75 13.8226H9.75L6.41249 16.0425C5.91749 16.3725 5.25 16.0201 5.25 15.4201V13.8226C3 13.8226 1.5 12.3226 1.5 10.0726V5.57251C1.5 3.32251 3 1.82251 5.25 1.82251H12.75C15 1.82251 16.5 3.32251 16.5 5.57251V10.0726C16.5 12.3226 15 13.8226 12.75 13.8226Z"
      stroke={color}
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.99998 8.52002V8.36255C8.99998 7.85255 9.315 7.58254 9.63 7.36504C9.9375 7.15504 10.245 6.88505 10.245 6.39005C10.245 5.70005 9.68998 5.14502 8.99998 5.14502C8.30998 5.14502 7.755 5.70005 7.755 6.39005"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.99662 10.3125H9.00337"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default Contact;
