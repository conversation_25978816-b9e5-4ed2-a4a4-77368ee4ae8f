import * as React from "react";

interface RolesProps {
  className?: string;
  color?: string;
  size?: number;
}

const Roles = ({
  className,
  color = "white",
  size = 18,
}: RolesProps): React.JSX.Element => (
  <svg
    className={className}
    height={size}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.75751 2.18999L14.1825 4.15499C15.4575 4.71749 15.4575 5.64749 14.1825 6.20999L9.75751 8.17499C9.25501 8.39999 8.43001 8.39999 7.92751 8.17499L3.50251 6.20999C2.22751 5.64749 2.22751 4.71749 3.50251 4.15499L7.92751 2.18999C8.43001 1.96499 9.25501 1.96499 9.75751 2.18999Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.25 8.25C2.25 8.88 2.7225 9.6075 3.3 9.8625L8.3925 12.1275C8.7825 12.3 9.225 12.3 9.6075 12.1275L14.7 9.8625C15.2775 9.6075 15.75 8.88 15.75 8.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.25 12C2.25 12.6975 2.6625 13.3275 3.3 13.6125L8.3925 15.8775C8.7825 16.05 9.225 16.05 9.6075 15.8775L14.7 13.6125C15.3375 13.3275 15.75 12.6975 15.75 12"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default Roles;
