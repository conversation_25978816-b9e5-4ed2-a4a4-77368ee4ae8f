import type { Column } from '@tanstack/react-table';
import { ArrowDown, ArrowUp, ChevronsUpDown } from 'lucide-react';
import { cn } from '../../lib/utils';
import { useIsMobile } from '../../hooks';

interface DataTableColumnHeaderProps<TData, TValue> extends React.HTMLAttributes<HTMLDivElement> {
  column: Column<TData, TValue>;
  textAlign?: 'left' | 'center' | 'right';
  headerTitle?: React.ReactNode;
}

export function DataTableColumnHeader<TData, TValue>({
  column,
  title,
  textAlign = 'left',
  headerTitle,
  className,
}: DataTableColumnHeaderProps<TData, TValue>) {
  const isMobile = useIsMobile();
  if (!column.getCanSort()) {
    return (
      <div
        className={cn(
          className,
          textAlign === 'right' && 'text-right',
          textAlign === 'center' && 'text-center',
        )}
      >
        {title || headerTitle}
      </div>
    );
  }

  return (
    <div
      className={cn(
        'flex items-center space-x-2',
        className,
        textAlign === 'right' && 'justify-end',
        textAlign === 'center' && 'justify-center',
      )}
    >
      <div
        className={cn(
          'h-8 data-[state=open]:bg-accent flex items-center cursor-pointer text-[#67748E]',
          textAlign === 'right' && 'justify-end',
          textAlign === 'center' && 'justify-center',
        )}
        onClick={isMobile ? undefined : column.getToggleSortingHandler()}
      >
        <span
          className={cn(
            'text-[0.8rem]',
            textAlign === 'right' && 'text-right',
            textAlign === 'center' && 'text-center',
          )}
        >
          {title || headerTitle}
        </span>
        {!isMobile && (
          <>
            {column.getIsSorted() === 'desc' ? (
              <ArrowDown className="ml-2 h-4 w-4" />
            ) : column.getIsSorted() === 'asc' ? (
              <ArrowUp className="ml-2 h-4 w-4" />
            ) : (
              <ChevronsUpDown className="ml-2 h-4 w-4" />
            )}
          </>
        )}
      </div>
    </div>
  );
}
