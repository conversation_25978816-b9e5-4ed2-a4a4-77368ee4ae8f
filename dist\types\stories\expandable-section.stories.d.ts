import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ExpandableSection } from "../components/design-system/expandable-section";
declare const meta: Meta<typeof ExpandableSection>;
export default meta;
type Story = StoryObj<typeof ExpandableSection>;
export declare const Default: Story;
export declare const DefaultOpen: Story;
export declare const WithWarning: Story;
export declare const WithFormContent: Story;
//# sourceMappingURL=expandable-section.stories.d.ts.map