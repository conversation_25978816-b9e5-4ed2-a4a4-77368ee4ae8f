import Cross from "../../assets/svg/Cross";
import ToastIcons from "../../assets/svg/toastIcons";
import { cn } from "../../lib/utils";
import { useEffect, useRef } from "react";
import type { ToastContentProps } from "react-toastify";

type CustomNotificationProps = ToastContentProps<{
  type: "success" | "warning" | "error" | "info";
  title: string;
  content?: string;
}>;

export default function Toaster({
  closeToast,
  data,
  toastProps,
}: CustomNotificationProps) {
  const progressRef = useRef<HTMLDivElement>(null);
  const duration =
    typeof toastProps?.autoClose === "number" ? toastProps.autoClose : null;

  useEffect(() => {
    if (progressRef.current && duration) {
      progressRef.current.style.transition = "none";
      progressRef.current.style.width = "100%";
      void progressRef.current.offsetWidth;
      progressRef.current.style.transition = `width ${duration}ms linear`;
      progressRef.current.style.width = "0%";

      const timer = setTimeout(() => {
        if (closeToast) closeToast();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration, closeToast]);

  const barColors: Record<string, string> = {
    success: "#15A48A",
    warning: "#F6B031",
    error: "#FE494D",
    info: "#2E86DB",
  };

  return (
    <div
      className={cn(
        "flex flex-col w-full p-3 rounded-md gap-1.5 relative",
        data.type === "success" && "bg-[#F0FAF9]",
        data.type === "warning" && "bg-[#FFFAF2]",
        data.type === "error" && "bg-[#FFF6F7]",
        data.type === "info" && "bg-[#F0F5FF]"
      )}
    >
      {duration && (
        <div
          ref={progressRef}
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            height: "4px",
            width: "100%",
            background: barColors[data.type],
            borderTopLeftRadius: 6,
            borderTopRightRadius: 6,
            transition: "none",
            zIndex: 2,
          }}
        />
      )}

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="bg-transparent shadow-none [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4">
            <ToastIcons type={data.type} />
          </span>
          <span
            className={cn(
              "text-[0.875rem] font-medium",
              data.type === "success" && "text-[#15A48A]",
              data.type === "warning" && "text-[#F6B031]",
              data.type === "error" && "text-[#FE494D]",
              data.type === "info" && "text-[#2E86DB]"
            )}
          >
            {data.title}
          </span>
        </div>
        <button
          className="bg-transparent shadow-none  [&_svg:not([class*='size-'])]:size-2.5"
          onClick={closeToast}
          title="Close"
        >
          <Cross />
        </button>
      </div>
      <div className="text-sm text-[#50585B] ml-6 mr-3">{data.content}</div>
    </div>
  );
}
