import type { <PERSON>a, <PERSON>Obj } from "@storybook/react";
import {
  FormTable,
  FormTableRow,
  FormTableHeader,
  FormTableCell,
  FormTableDeleteCell,
} from "../components/design-system/form-table";

const meta: Meta<typeof FormTable> = {
  title: "Design System/FormTable",
  component: FormTable,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof FormTable>;

export const Default: Story = {
  render: () => (
    <FormTable>
      <FormTableRow>
        <FormTableHeader>Name</FormTableHeader>
        <FormTableHeader>Email</FormTableHeader>
        <FormTableHeader>Role</FormTableHeader>
        <FormTableHeader></FormTableHeader>
      </FormTableRow>
      <FormTableRow>
        <FormTableCell>John Doe</FormTableCell>
        <FormTableCell><EMAIL></FormTableCell>
        <FormTableCell>Admin</FormTableCell>
      </FormTableRow>
      <FormTableRow>
        <FormTableCell><PERSON></FormTableCell>
        <FormTableCell><EMAIL></FormTableCell>
        <FormTableCell>User</FormTableCell>
      </FormTableRow>
    </FormTable>
  ),
};

export const WithCustomStyling: Story = {
  render: () => (
    <FormTable className="border rounded-lg">
      <FormTableRow>
        <FormTableHeader className="bg-gray-50">Product</FormTableHeader>
        <FormTableHeader className="bg-gray-50">Price</FormTableHeader>
        <FormTableHeader className="bg-gray-50">Stock</FormTableHeader>
        <FormTableHeader className="bg-gray-50"></FormTableHeader>
      </FormTableRow>
      <FormTableRow>
        <FormTableCell>Product A</FormTableCell>
        <FormTableCell>$99.99</FormTableCell>
        <FormTableCell>In Stock</FormTableCell>
        <FormTableDeleteCell onClick={() => console.log("Delete clicked")} />
      </FormTableRow>
      <FormTableRow>
        <FormTableCell>Product B</FormTableCell>
        <FormTableCell>$149.99</FormTableCell>
        <FormTableCell>Low Stock</FormTableCell>
        <FormTableDeleteCell onClick={() => console.log("Delete clicked")} />
      </FormTableRow>
    </FormTable>
  ),
};

export const WithInputs: Story = {
  render: () => (
    <FormTable className="border rounded-lg">
      <FormTableRow>
        <FormTableHeader className="bg-gray-50">Name</FormTableHeader>
        <FormTableHeader className="bg-gray-50">Username</FormTableHeader>
        <FormTableHeader className="bg-gray-50">Role</FormTableHeader>
        <FormTableHeader className="bg-gray-50"></FormTableHeader>
      </FormTableRow>
      <FormTableRow>
        <FormTableCell>
          <input type="text" placeholder="Enter name" />
        </FormTableCell>
        <FormTableCell>
          <input type="text" placeholder="Enter username" />
        </FormTableCell>
        <FormTableCell>
          <input type="text" placeholder="Enter role" />
        </FormTableCell>
        <FormTableDeleteCell onClick={() => console.log("Delete clicked")} />
      </FormTableRow>
    </FormTable>
  ),
};
