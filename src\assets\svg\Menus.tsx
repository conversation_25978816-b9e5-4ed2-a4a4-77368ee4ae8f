import * as React from "react";

interface MenusProps {
  className?: string;
  color?: string;
  size?: number;
}

const Menus = ({
  className,
  color = "black",
  size = 18,
}: MenusProps): React.JSX.Element => (
  <svg
    className={className}
    width={size}
    height={size}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path stroke={color} strokeWidth="1.5" strokeLinecap="round" />
    <path
      d="M15.54 9.75V12.75H2.45251V9.75C2.45251 6.87 4.48501 4.4625 7.19251 3.885C7.59751 3.795 8.01751 3.75 8.45251 3.75H9.54002C9.97502 3.75 10.4025 3.795 10.8075 3.885C13.515 4.47 15.54 6.87 15.54 9.75Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.875 3.375C10.875 3.555 10.8525 3.72 10.8075 3.885C10.4025 3.795 9.975 3.75 9.54 3.75H8.4525C8.0175 3.75 7.5975 3.795 7.1925 3.885C7.1475 3.72 7.125 3.555 7.125 3.375C7.125 2.34 7.965 1.5 9 1.5C10.035 1.5 10.875 2.34 10.875 3.375Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.25 8.25H6.75"
      stroke="#161618"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default Menus;
