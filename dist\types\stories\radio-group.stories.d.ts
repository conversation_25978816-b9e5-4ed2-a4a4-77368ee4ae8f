import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { RadioItemGroup } from "../components/design-system/radio-group";
declare const meta: Meta<typeof RadioItemGroup>;
export default meta;
type Story = StoryObj<typeof RadioItemGroup>;
export declare const Default: Story;
export declare const WithCustomStyling: Story;
export declare const Disabled: Story;
//# sourceMappingURL=radio-group.stories.d.ts.map