import * as React from "react";

import { NavMain, type NavMainItem } from "./nav-main";
import { AppSwitcher, type sidebarApps } from "./app-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarLogo,
  SidebarRail,
  SidebarTrigger,
  useSidebar,
} from "../design-system/sidebar";

export function AppSidebar({
  sidebarApps,
  sidebarNavMain,
  sidebarFooter,
  activeApp,
  setActiveApp,
  logo,
  logoExpanded,
  sidebarCollapsed,
  sidebarExpanded,
  ...props
}: React.ComponentProps<typeof Sidebar> & {
  sidebarApps: sidebarApps[];
  sidebarNavMain: NavMainItem[];
  sidebarFooter: React.ReactNode;
  activeApp: sidebarApps;
  setActiveApp: (app: sidebarApps) => void;
  logo: string;
  logoExpanded: string;
  sidebarCollapsed: string;
  sidebarExpanded: string;
}) {
  const { visible } = useSidebar();

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <div className="flex items-center justify-between">
          <SidebarLogo logo={logo} logoExpanded={logoExpanded} />
          {visible && (
            <SidebarTrigger
              className="-ml-1"
              sidebarCollapsed={sidebarCollapsed}
              sidebarExpanded={sidebarExpanded}
            />
          )}
        </div>
        {sidebarApps && (
          <AppSwitcher
            apps={sidebarApps}
            activeApp={activeApp}
            setActiveApp={setActiveApp}
          />
        )}
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={sidebarNavMain} />
      </SidebarContent>
      {sidebarFooter && <SidebarFooter>{sidebarFooter}</SidebarFooter>}
      <SidebarRail />
    </Sidebar>
  );
}
