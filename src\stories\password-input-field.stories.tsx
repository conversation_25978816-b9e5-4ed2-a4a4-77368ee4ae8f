import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { PasswordInput } from "../components/design-system/form-password-input";

const meta: Meta<typeof PasswordInput> = {
  title: "Design System/PasswordInput",
  component: PasswordInput,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: { control: "text" },
    placeholder: { control: "text" },
    value: { control: "text" },
    disabled: { control: "boolean" },
    required: { control: "boolean" },
    error: { control: "boolean" },
    helperText: { control: "text" },
    onChange: { action: "changed" },
  },
};

export default meta;
type Story = StoryObj<typeof PasswordInput>;

export const Default: Story = {
  args: {
    label: "Password",
    placeholder: "Enter your password",
  },
};

export const WithValue: Story = {
  args: {
    label: "Password",
    value: "password123",
  },
};

export const Required: Story = {
  args: {
    label: "Password",
    required: true,
  },
};

export const WithError: Story = {
  args: {
    label: "Password",
    error: true,
    helperText: "Password is required",
  },
};

export const Disabled: Story = {
  args: {
    label: "Password",
    value: "password123",
    disabled: true,
  },
};
