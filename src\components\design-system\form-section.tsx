import React from "react";
import { But<PERSON> } from "../ui/button";

interface FormSectionProps {
  children: React.ReactNode;
  title: string;
  onAddNew?: () => void;
}

function FormSection({ children, title, onAddNew }: FormSectionProps) {
  return (
    <div className="flex flex-col w-full rounded-md border border-[#CBD5E1]">
      <span className="text-xs bg-[#CBD5E166] px-4 py-3 w-full uppercase">
        {title}
      </span>
      <div className="px-4 py-1">{children}</div>
      {onAddNew && (
        <Button
          className="self-start bg-transparent hover:bg-transparent"
          onClick={onAddNew}
        >
          <span className="text-[#EB062B] font-medium text-[0.625rem]">
            + ADD NEW
          </span>
        </Button>
      )}
    </div>
  );
}

export default FormSection;
