import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { InputField } from "../index";
declare const meta: Meta<typeof InputField>;
export default meta;
type Story = StoryObj<typeof InputField>;
export declare const Default: Story;
export declare const Required: Story;
export declare const WithWarning: Story;
export declare const Disabled: Story;
export declare const WithValue: Story;
//# sourceMappingURL=input-field.stories.d.ts.map