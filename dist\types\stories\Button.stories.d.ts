import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { But<PERSON> } from "../components/ui/button";
declare const meta: Meta<typeof Button>;
export default meta;
type Story = StoryObj<typeof Button>;
export declare const Default: Story;
export declare const Outline: Story;
export declare const Ghost: Story;
export declare const Link: Story;
export declare const Small: Story;
export declare const Large: Story;
export declare const Disabled: Story;
export declare const WithIcon: Story;
//# sourceMappingURL=Button.stories.d.ts.map