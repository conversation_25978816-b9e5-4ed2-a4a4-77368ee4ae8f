import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { InputField } from "../index";

const meta: Meta<typeof InputField> = {
  title: "Design System/InputField",
  component: InputField,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: { control: "text" },
    placeholder: { control: "text" },
    value: { control: "text" },
    disabled: { control: "boolean" },
    required: { control: "boolean" },
    helperText: { control: "text" },
    onChange: { action: "changed" },
  },
};

export default meta;
type Story = StoryObj<typeof InputField>;

export const Default: Story = {
  args: {
    label: "Input Label",
    placeholder: "Enter text here",
  },
};

export const Required: Story = {
  args: {
    label: "Required Input",
    placeholder: "This field is required",
    required: true,
  },
};

export const WithWarning: Story = {
  args: {
    label: "Input with Warning",
    placeholder: "This field has a warning",
    helperText: "Required Field",
  },
};

export const Disabled: Story = {
  args: {
    label: "Disabled Input",
    placeholder: "This input is disabled",
    disabled: true,
  },
};

export const WithValue: Story = {
  args: {
    label: "Input with Value",
    value: "This is a value",
  },
};
