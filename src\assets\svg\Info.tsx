import * as React from "react";

interface InfoProps {
  className?: string;
  color?: string;
  size?: number;
}
const Info = ({
  className,
  color = "#2E86DB",
  size = 16,
}: InfoProps): React.JSX.Element => {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 16.5C13.1325 16.5 16.5 13.1325 16.5 9C16.5 4.8675 13.1325 1.5 9 1.5C4.8675 1.5 1.5 4.8675 1.5 9C1.5 13.1325 4.8675 16.5 9 16.5ZM9.5625 12C9.5625 12.3075 9.3075 12.5625 9 12.5625C8.6925 12.5625 8.4375 12.3075 8.4375 12L8.4375 8.25C8.4375 7.9425 8.6925 7.6875 9 7.6875C9.3075 7.6875 9.5625 7.9425 9.5625 8.25V12ZM8.31 5.715C8.3475 5.6175 8.4 5.5425 8.4675 5.4675C8.5425 5.4 8.625 5.3475 8.715 5.31C8.805 5.2725 8.9025 5.25 9 5.25C9.0975 5.25 9.195 5.2725 9.285 5.31C9.375 5.3475 9.4575 5.4 9.5325 5.4675C9.6 5.5425 9.6525 5.6175 9.69 5.715C9.7275 5.805 9.75 5.9025 9.75 6C9.75 6.0975 9.7275 6.195 9.69 6.285C9.6525 6.375 9.6 6.4575 9.5325 6.5325C9.4575 6.6 9.375 6.6525 9.285 6.69C9.105 6.765 8.895 6.765 8.715 6.69C8.625 6.6525 8.5425 6.6 8.4675 6.5325C8.4 6.4575 8.3475 6.375 8.31 6.285C8.2725 6.195 8.25 6.0975 8.25 6C8.25 5.9025 8.2725 5.805 8.31 5.715Z"
        fill={color}
      />
    </svg>
  );
};

export default Info;
