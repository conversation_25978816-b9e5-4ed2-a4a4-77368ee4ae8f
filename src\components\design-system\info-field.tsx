import { cn } from "../../lib/utils";

type InfoFieldProps = {
  label: string;
  value: React.ReactNode;
  className?: string;
};

export const InfoField = ({ label, value }: InfoFieldProps) => (
  <div className="flex flex-col gap-1">
    <p className="capitalize text-xs opacity-60">{label.replace(/_/g, " ")}</p>
    <p className="text-xs font-medium">{String(value)?.trim() ? value : "-"}</p>
  </div>
);

export const InfoTextField = ({ label, value, className }: InfoFieldProps) => (
  <div className="flex flex-col gap-1">
    <span className="text-xs opacity-60">
      {label.replace(/_/g, " ")}
    </span>
    <div
      className={cn(
        "bg-[#F1F5F9] text-black py-3 px-2.5 text-xs border border-[#D8D8D8] rounded-md font-medium",
        className
      )}
    >
      {typeof value === "string"
        ? value.trim()
          ? value
          : "-"
        : value
        ? value
        : "-"}
    </div>
  </div>
);
