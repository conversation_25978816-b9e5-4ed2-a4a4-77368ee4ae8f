import React from "react";

interface CrossProps {
  className?: string;
  color?: string;
  size?: number;
}

const Cross = ({
  className,
  color = "black",
  size = 12,
}: CrossProps): React.JSX.Element => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 12 13"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M7.09938 6.50973L11.7719 1.83703C12.076 1.53309 12.076 1.04166 11.7719 0.737721C11.468 0.433781 10.9766 0.433781 10.6726 0.737721L5.99993 5.41043L1.32737 0.737721C1.02329 0.433781 0.532002 0.433781 0.228062 0.737721C-0.0760206 1.04166 -0.0760206 1.53309 0.228062 1.83703L4.90062 6.50973L0.228062 11.1824C-0.0760206 11.4864 -0.0760206 11.9778 0.228062 12.2817C0.379534 12.4334 0.578696 12.5095 0.777716 12.5095C0.976737 12.5095 1.17576 12.4334 1.32737 12.2817L5.99993 7.60904L10.6726 12.2817C10.8243 12.4334 11.0233 12.5095 11.2223 12.5095C11.4213 12.5095 11.6203 12.4334 11.7719 12.2817C12.076 11.9778 12.076 11.4864 11.7719 11.1824L7.09938 6.50973Z"
      fill={color}
    />
  </svg>
);

export default Cross;
