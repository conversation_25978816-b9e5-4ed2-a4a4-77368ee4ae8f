import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { SearchSingleSelectDropdown } from "../components/design-system/search-single-select-dropdown";
declare const meta: Meta<typeof SearchSingleSelectDropdown>;
export default meta;
type Story = StoryObj<typeof SearchSingleSelectDropdown>;
export declare const Default: Story;
export declare const WithSelection: Story;
export declare const Required: Story;
export declare const WithWarning: Story;
export declare const WithLongOptions: Story;
export declare const WithCustomClassName: Story;
//# sourceMappingURL=search-single-select-dropdown.stories.d.ts.map