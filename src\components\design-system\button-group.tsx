import { useIsMobile } from "../../hooks/use-mobile";
import { Button } from "../ui/button";

interface props {
  onConfirm: () => void;
  onCancel: () => void;
  confirmText: string;
  cancelText: string;
  isLoading?: boolean;
}

const ButtonGroup = ({
  onConfirm,
  onCancel,
  confirmText,
  cancelText,
  isLoading,
}: props) => {
  const isMobile = useIsMobile();
  return isMobile ? (
    <div className="flex gap-2 py-2 w-full z-10">
      <Button
        className="flex-1"
        variant="outline"
        color="destructive"
        onClick={onConfirm}
        isLoading={isLoading}
      >
        {confirmText}
      </Button>
      <Button
        className="flex-1"
        variant="outline"
        color="muted"
        onClick={onCancel}
      >
        {cancelText}
      </Button>
    </div>
  ) : (
    <div className="flex gap-2 mt-7">
      <Button
        variant="contained"
        color="destructive"
        onClick={onConfirm}
        isLoading={isLoading}
      >
        {confirmText}
      </Button>
      <Button variant="outline" color="muted" onClick={onCancel}>
        {cancelText}
      </Button>
    </div>
  );
};

export default ButtonGroup;
