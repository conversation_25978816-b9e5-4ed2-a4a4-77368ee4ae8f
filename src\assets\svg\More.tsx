import React from "react";

interface MoreProps {
  color?: string;
  size?: number;
  className?: string;
}

const More = ({
  color = "#031E2E",
  size = 20,
  className,
}: MoreProps): React.JSX.Element => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M4.16667 12.2917C2.9 12.2917 1.875 11.2667 1.875 10C1.875 8.73333 2.9 7.70833 4.16667 7.70833C5.43333 7.70833 6.45833 8.73333 6.45833 10C6.45833 11.2667 5.43333 12.2917 4.16667 12.2917ZM4.16667 8.95833C3.59167 8.95833 3.125 9.425 3.125 10C3.125 10.575 3.59167 11.0417 4.16667 11.0417C4.74167 11.0417 5.20833 10.575 5.20833 10C5.20833 9.425 4.74167 8.95833 4.16667 8.95833Z"
      fill={color}
    />
    <path
      d="M15.8334 12.2917C14.5667 12.2917 13.5417 11.2667 13.5417 10C13.5417 8.73333 14.5667 7.70833 15.8334 7.70833C17.1001 7.70833 18.1251 8.73333 18.1251 10C18.1251 11.2667 17.1001 12.2917 15.8334 12.2917ZM15.8334 8.95833C15.2584 8.95833 14.7917 9.425 14.7917 10C14.7917 10.575 15.2584 11.0417 15.8334 11.0417C16.4084 11.0417 16.8751 10.575 16.8751 10C16.8751 9.425 16.4084 8.95833 15.8334 8.95833Z"
      fill={color}
    />
    <path
      d="M9.99992 12.2917C8.73325 12.2917 7.70825 11.2667 7.70825 10C7.70825 8.73333 8.73325 7.70833 9.99992 7.70833C11.2666 7.70833 12.2916 8.73333 12.2916 10C12.2916 11.2667 11.2666 12.2917 9.99992 12.2917ZM9.99992 8.95833C9.42492 8.95833 8.95825 9.425 8.95825 10C8.95825 10.575 9.42492 11.0417 9.99992 11.0417C10.5749 11.0417 11.0416 10.575 11.0416 10C11.0416 9.425 10.5749 8.95833 9.99992 8.95833Z"
      fill={color}
    />
  </svg>
);

export default More;
