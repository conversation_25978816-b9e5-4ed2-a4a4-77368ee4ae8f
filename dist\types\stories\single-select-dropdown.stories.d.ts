import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { SingleSelectDropdown } from "../components/design-system/single-select-dropdown";
declare const meta: Meta<typeof SingleSelectDropdown>;
export default meta;
type Story = StoryObj<typeof SingleSelectDropdown>;
export declare const Default: Story;
export declare const WithValue: Story;
export declare const Required: Story;
export declare const WithError: Story;
export declare const CustomPlaceholder: Story;
//# sourceMappingURL=single-select-dropdown.stories.d.ts.map