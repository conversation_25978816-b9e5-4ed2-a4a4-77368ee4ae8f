import { useCallback, useEffect } from "react";
import { useConfirm } from "./use-confirm";
import { useBlocker } from "react-router-dom";

export const useDiscardChanges = (isDirty: boolean) => {
  const { confirm } = useConfirm();

  // Block navigation when there are unsaved changes
  const blocker = useBlocker(isDirty);

  const handleBlockedNavigation = useCallback(async () => {
    if (blocker.state === "blocked" && isDirty) {
      const shouldProceed = await confirm({
        title: "Unsaved Changes",
        message: "You have unsaved changes. Do you wish to discard changes?",
        secondaryButtonText: "No, Stay on page",
        primaryButtonText: "Yes, Discard changes",
      });

      if (shouldProceed) {
        blocker.proceed();
      } else {
        blocker.reset();
      }
    }
  }, [blocker, confirm, isDirty]);

  useEffect(() => {
    if (blocker.state === "blocked") {
      handleBlockedNavigation();
    }
  }, [blocker.state, handleBlockedNavigation]);

  const discardChanges = useCallback(
    async (onProceed: () => void) => {
      if (!isDirty) {
        onProceed();
        return;
      }

      const shouldProceed = await confirm({
        title: "Unsaved Changes",
        message: "You have unsaved changes. Do you wish to discard changes?",
        primaryButtonText: "No, Stay on page",
        secondaryButtonText: "Yes, Discard changes",
      });

      if (shouldProceed) {
        onProceed();
      }
    },
    [confirm, isDirty]
  );

  return discardChanges;
};
