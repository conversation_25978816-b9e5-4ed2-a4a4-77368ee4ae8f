import React from "react";

interface GlobeProps {
  className?: string;
  color?: string;
  size?: number;
}

const Globe = ({
  className,
  color = "black",
  size = 18,
}: GlobeProps): React.JSX.Element => {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 18 19"
      fill="none"
    >
      <path
        d="M9 17C13.1421 17 16.5 13.6421 16.5 9.5C16.5 5.35786 13.1421 2 9 2C4.85786 2 1.5 5.35786 1.5 9.5C1.5 13.6421 4.85786 17 9 17Z"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.99995 2.75H6.74995C5.28745 7.13 5.28745 11.87 6.74995 16.25H5.99995"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.25 2.75C12.7125 7.13 12.7125 11.87 11.25 16.25"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.25 12.5V11.75C6.63 13.2125 11.37 13.2125 15.75 11.75V12.5"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.25 7.25001C6.63 5.78751 11.37 5.78751 15.75 7.25001"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default Globe;
