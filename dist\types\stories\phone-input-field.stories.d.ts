import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import PhoneInputField from "../components/design-system/phone-input-field";
declare const meta: Meta<typeof PhoneInputField>;
export default meta;
type Story = StoryObj<typeof PhoneInputField>;
export declare const Default: Story;
export declare const WithValue: Story;
export declare const Required: Story;
export declare const WithHelperText: Story;
export declare const Disabled: Story;
export declare const WithLongExtension: Story;
//# sourceMappingURL=phone-input-field.stories.d.ts.map