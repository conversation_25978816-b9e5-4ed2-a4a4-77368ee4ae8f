import * as React from "react";
import { ChevronDown } from "lucide-react";
import { cn } from "../../lib/utils";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Button } from "../ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "../ui/command";
import InfiniteScrollList from "./infinite-scroll-list";

export type Option = {
  label: string;
  value: string;
};

interface PaginatedSearchSingleSelectProps {
  label?: string;
  options: Option[];
  value: string | undefined;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;

  onSearch: (searchText: string) => void;
  isLoading?: boolean;
  hasMore?: boolean;
  loadMore?: () => void;
  loadError?: Error | string | null;
  text: {
    loading: string;
    noOptions: string;
  };
}

export const PaginatedSearchSingleSelectDropdown = React.forwardRef<
  HTMLButtonElement,
  PaginatedSearchSingleSelectProps
>(
  ({
    label,
    options,
    value,
    onChange,
    placeholder = "Select item",
    className,
    required = false,
    error: hasError,
    helperText,
    disabled = false,
    onSearch,
    isLoading = false,
    hasMore = false,
    loadMore,
    loadError,
    text,
  }) => {
    const [open, setOpen] = React.useState(false);
    const [searchText, setSearchText] = React.useState("");

    const handleSelect = (selectedValue: string) => {
      onChange(selectedValue);
      setOpen(false);
      setSearchText("");
    };

    const handleSearch = (searchValue: string) => {
      setSearchText(searchValue);
      onSearch(searchValue);
    };

    const selectedOption = options.find((option) => option.value === value);

    return (
      <div className="flex flex-col gap-1.25">
        {label && (
          <span className="text-xs">
            {label}
            {required && <span className="ml-0.25 text-destructive">*</span>}
          </span>
        )}
        <Popover
          open={open}
          onOpenChange={(newOpen) => {
            if (!newOpen) {
              setSearchText("");
            }
            if (!disabled) {
              setOpen(newOpen);
            }
          }}
        >
          <PopoverTrigger className="w-full" disabled={disabled}>
            <Button
              variant="outline"
              aria-expanded={open}
              disabled={disabled}
              className={cn(
                "w-full hover:bg-transparent hover:text-current text-left",
                className,
                hasError
                  ? "border-destructive focus:border-destructive focus:ring-destructive"
                  : "",
                disabled ? "opacity-50 cursor-not-allowed" : ""
              )}
            >
              <div className="flex gap-1 items-center text-xs text-left min-w-0 w-full p-0">
                {selectedOption ? (
                  <span className="truncate w-full">
                    {selectedOption.label}
                  </span>
                ) : (
                  <span className="text-[var(--placeholder)] truncate w-full">
                    {placeholder}
                  </span>
                )}
              </div>
              <ChevronDown
                className={cn(
                  "ml-2 h-5 w-5 shrink-0 transition-transform duration-200 text-[#696969]",
                  open && "rotate-180"
                )}
              />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            style={{ width: "var(--radix-popover-trigger-width)" }}
            align="start"
            className="w-full p-0 max-h-[12.5rem]"
            data-slot="dropdown-menu-content"
          >
            <Command shouldFilter={false}>
              <CommandInput
                placeholder="Search"
                value={searchText}
                onValueChange={handleSearch}
                className=""
              />
              <CommandList>
                {options.length === 0 ? (
                  <CommandEmpty>{text.noOptions}</CommandEmpty>
                ) : (
                  <CommandGroup>
                    <div className="max-h-[12.5rem] overflow-y-auto">
                      <InfiniteScrollList
                        hasMore={hasMore}
                        loadMore={loadMore || (() => {})}
                        isLoading={isLoading}
                        error={loadError}
                      >
                        {options.map((option) => {
                          return (
                            <CommandItem
                              data-slot="dropdown-menu-item"
                              key={option.value}
                              onSelect={() => handleSelect(option.value)}
                              className={cn(
                                "flex items-center gap-2 data-[selected=true]:bg-muted data-[selected=true]:text-muted-foreground",
                                value === option.value &&
                                  "bg-muted text-muted-foreground"
                              )}
                            >
                              <span>{option.label}</span>
                            </CommandItem>
                          );
                        })}
                        {isLoading && (
                          <div className="flex items-center justify-center py-2">
                            <span className="text-sm text-muted-foreground">
                              {text.loading}
                            </span>
                          </div>
                        )}
                      </InfiniteScrollList>
                    </div>
                  </CommandGroup>
                )}
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
        {hasError && (
          <span className="text-xs text-destructive">{helperText}</span>
        )}
      </div>
    );
  }
);

PaginatedSearchSingleSelectDropdown.displayName =
  "PaginatedSearchSingleSelectDropdown";
