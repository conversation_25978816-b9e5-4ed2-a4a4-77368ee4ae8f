import * as React from "react";
import { ChevronRight, ChevronDown } from "lucide-react";

import { cn } from "../../lib/utils";
import { Button } from "../ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "../ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Checkbox } from "../ui/check-box";

type DropdownOption = {
  value: string;
  label: string;
  items?: { value: string; label: string }[];
};

interface GroupedDropdownProps {
  label?: string;
  options: DropdownOption[];
  selected: string[];
  onChange: (selected: string[]) => void;
  placeholder?: string;
  className?: string;
  maxDisplayItems?: number;
  required?: boolean;
  disableHover?: boolean;
}

export function GroupedDropdown({
  label,
  options,
  selected,
  onChange,
  placeholder = "Select items",
  className,
  maxDisplayItems = 3,
  required = false,
  disableHover = false,
}: GroupedDropdownProps) {
  const [open, setOpen] = React.useState(false);
  const [expandedGroups, setExpandedGroups] = React.useState<
    Record<string, boolean>
  >(() => {
    const initialState: Record<string, boolean> = {};
    options.forEach((option) => {
      initialState[option.value] = true;
    });
    return initialState;
  });
  const [searchTerm, setSearchTerm] = React.useState("");

  const toggleGroup = (value: string, e: React.SyntheticEvent) => {
    e.stopPropagation();
    setExpandedGroups((prev) => ({
      ...prev,
      [value]: !prev[value],
    }));
  };

  const handleSelect = (value: string) => {
    const newSelected = selected.includes(value)
      ? selected.filter((v) => v !== value)
      : [...selected, value];
    onChange(newSelected);
  };

  const renderItem = (option: DropdownOption) => {
    const isExpanded = expandedGroups[option.value];

    return (
      <>
        <CommandItem
          value={option.value}
          onSelect={() => {
            toggleGroup(option.value, {
              stopPropagation: () => {},
            } as React.SyntheticEvent);
          }}
          onClick={(e) => toggleGroup(option.value, e)}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " " || e.key === "Space") {
              toggleGroup(option.value, e);
            }
          }}
          className={cn(
            "flex items-center gap-2 cursor-pointer !text-[#949494] font-medium data-[selected=true]:bg-muted data-[selected=true]:text-muted-foreground"
          )}
          aria-expanded={isExpanded}
        >
          <ChevronRight
            className={cn(
              "ml-2 h-5 w-5 shrink-0 transition-transform duration-200 text-[#696969]",
              isExpanded && "rotate-90"
            )}
          />
          <span className="ml-1">{option.label}</span>
        </CommandItem>

        {isExpanded &&
          option.items?.map((item) => {
            if (searchTerm) {
              const itemMatches =
                item.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.value.toLowerCase().includes(searchTerm.toLowerCase());

              if (!itemMatches) return null;
            }

            return (
              <div className="flex items-center w-full pl-4">
                <Checkbox
                  checked={selected.includes(item.value)}
                  className="mr-1 h-4 w-4 "
                />
                <CommandItem
                  key={item.value}
                  onSelect={() => handleSelect(item.value)}
                  className={cn(
                    "font-medium w-full data-[selected=true]:bg-muted data-[selected=true]:text-muted-foreground"
                  )}
                >
                  {item.label}
                </CommandItem>
              </div>
            );
          })}
      </>
    );
  };

  return (
    <div className="flex flex-col gap-1.25 text-left">
      {label && (
        <span className="text-xs text-black">
          {label}
          <span className="ml-0.25">
            {required && <span className="text-destructive">*</span>}
          </span>
        </span>
      )}
      <Popover
        open={open}
        onOpenChange={(newOpen) => {
          if (!newOpen) {
            setSearchTerm("");
          }
          setOpen(newOpen);
        }}
      >
        <PopoverTrigger className="w-full">
          <Button
            variant="outline"
            role="combobox"
            tabIndex={-1}
            aria-expanded={open}
            className={cn(
              "w-full bg-white text-left border border-input",
              !disableHover && "hover:bg-white hover:text-current",
              className
            )}
          >
            <div
              className="flex flex-wrap gap-1 items-center text-xs text-left min-w-0 w-full "
              title={selected.join(", ")}
            >
              {selected.length === 0 ? (
                <span className="text-[var(--placeholder)] truncate w-full">
                  {placeholder}
                </span>
              ) : (
                <span className="truncate text-black w-full">
                  {selected
                    .slice(0, maxDisplayItems)
                    .map((val) => {
                      const found = options
                        .flatMap((g) => g.items || [])
                        .find((i) => i.value === val);
                      return found?.label ?? val;
                    })
                    .join(", ")}

                  {selected.length > maxDisplayItems && (
                    <span className="pl-1">
                      +{selected.length - maxDisplayItems} more
                    </span>
                  )}
                </span>
              )}
            </div>
            <ChevronDown
              className={cn(
                "ml-2 h-5 w-5 shrink-0 transition-transform duration-200 text-[#696969]",
                open && "rotate-180"
              )}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          style={{ width: "var(--radix-popover-trigger-width)" }}
          className="w-full p-0"
          align="start"
        >
          <Command
            filter={() => {
              return 1;
            }}
          >
            <CommandInput
              placeholder="Search options..."
              value={searchTerm}
              onValueChange={setSearchTerm}
            />
            <CommandList>
              <CommandEmpty>No options found.</CommandEmpty>
              <CommandGroup>
                {searchTerm
                  ? options.map((option) => {
                      const matchingItems = option.items?.filter((item) =>
                        item.label
                          .toLowerCase()
                          .includes(searchTerm.toLowerCase())
                      );

                      if (matchingItems && matchingItems.length > 0) {
                        const filteredOption = {
                          ...option,
                          items: matchingItems,
                        };
                        return renderItem(filteredOption);
                      }

                      if (
                        option.label
                          .toLowerCase()
                          .includes(searchTerm.toLowerCase())
                      ) {
                        return renderItem(option);
                      }

                      return null;
                    })
                  : options.map((option, index, arr) => (
                      <React.Fragment key={option.value}>
                        {renderItem(option)}
                        {index < arr.length - 1 && (
                          <div className="my-1 border-t border-[#E8E8E8]" />
                        )}
                      </React.Fragment>
                    ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
