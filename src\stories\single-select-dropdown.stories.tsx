import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { SingleSelectDropdown } from "../components/design-system/single-select-dropdown";

const meta: Meta<typeof SingleSelectDropdown> = {
  title: "Design System/SingleSelectDropdown",
  component: SingleSelectDropdown,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: { control: "text" },
    value: { control: "text" },
    placeholder: { control: "text" },
    required: { control: "boolean" },
    helperText: { control: "text" },
    onChange: { action: "changed" },
  },
};

export default meta;
type Story = StoryObj<typeof SingleSelectDropdown>;

const options = [
  { value: "option1", label: "Option 1" },
  { value: "option2", label: "Option 2" },
  { value: "option3", label: "Option 3" },
];

export const Default: Story = {
  args: {
    label: "Select an option",
    value: "",
    options,
    placeholder: "Choose an option",
  },
};

export const WithValue: Story = {
  args: {
    label: "Select an option",
    value: "option1",
    options,
  },
};

export const Required: Story = {
  args: {
    label: "Required Selection",
    value: "",
    options,
    required: true,
  },
};

export const WithError: Story = {
  args: {
    label: "Selection with Error",
    value: "",
    options,
    helperText: "This field is required",
  },
};

export const CustomPlaceholder: Story = {
  args: {
    label: "Custom Placeholder",
    value: "",
    options,
    placeholder: "Please select an option...",
  },
};
