import React from "react";

interface LinkSvgProps {
  className?: string;
  color?: string;
  size?: number;
}

const LinkSvg = ({
  className,
  color = "white",
  size = 18,
}: LinkSvgProps): React.JSX.Element => (
  <svg
    className={className}
    width={size}
    height={size}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.2425 13.125H12.375C14.64 13.125 16.5 11.2725 16.5 9C16.5 6.735 14.6475 4.875 12.375 4.875H11.2425"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.75 4.875H5.625C3.3525 4.875 1.5 6.7275 1.5 9C1.5 11.265 3.3525 13.125 5.625 13.125H6.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6 9H12"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default LinkSvg;
