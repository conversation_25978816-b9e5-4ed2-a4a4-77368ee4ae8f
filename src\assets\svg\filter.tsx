import React from "react";
interface FilterProps {
  className?: string;
  color?: string;
  size?: number;
}

const Filter = ({
  className,
  color = "black",
  size = 18,
}: FilterProps): React.JSX.Element => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M10.7399 14.3025C10.7399 14.76 10.4399 15.36 10.0574 15.5925L8.99992 16.275C8.01742 16.8825 6.65242 16.2 6.65242 14.985V10.9725C6.65242 10.44 6.35242 9.75751 6.04492 9.38251L3.1649 6.3525C2.7824 5.97 2.48242 5.29501 2.48242 4.83751V3.0975C2.48242 2.19 3.16494 1.50751 3.99744 1.50751H14.0024C14.8349 1.50751 15.5174 2.19 15.5174 3.0225V4.6875C15.5174 5.295 15.1349 6.05251 14.7599 6.42751"
        stroke={color}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.0526 12.39C13.3781 12.39 14.4526 11.3155 14.4526 9.99001C14.4526 8.66452 13.3781 7.59 12.0526 7.59C10.7271 7.59 9.65259 8.66452 9.65259 9.99001C9.65259 11.3155 10.7271 12.39 12.0526 12.39Z"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.9026 12.84L14.1526 12.09"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default Filter;
