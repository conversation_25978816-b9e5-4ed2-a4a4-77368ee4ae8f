import { ToastContainer as ReactToastifyContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

export default function ToastContainer({
  ...props
}: React.ComponentProps<typeof ReactToastifyContainer>) {
  return (
    <ReactToastifyContainer
      position="top-center"
      newestOnTop={false}
      autoClose={3000}
      closeOnClick={false}
      rtl={false}
      pauseOnHover
      theme="light"
      {...props}
    />
  );
}
