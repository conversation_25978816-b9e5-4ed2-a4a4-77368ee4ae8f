"use client";

import type React from "react";
import { useState } from "react";
import { ChevronDown } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "../ui/collapsible";
import WarningIcon from "../../assets/svg/warning";

interface ExpandableSectionProps {
  title?: string;
  defaultOpen?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  showWarning?: boolean;
  children: React.ReactNode;
}

export function ExpandableSection({
  title = "Section",
  defaultOpen = false,
  open,
  onOpenChange,
  showWarning = false,
  children,
}: ExpandableSectionProps) {
  const isControlled = open !== undefined;
  const [internalOpen, setInternalOpen] = useState(defaultOpen);
  const isOpen = isControlled ? open : internalOpen;
  const handleToggle = (nextOpen: boolean) => {
    if (!isControlled) {
      setInternalOpen(nextOpen);
    }
    onOpenChange?.(nextOpen);
  };

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={handleToggle}
      className="w-full rounded-lg bg-white shadow-sm"
    >
      <CollapsibleTrigger className="flex w-full items-center justify-between py-3 px-5">
        <span className="text-sm font-medium uppercase tracking-wide text-[#696969] flex items-center gap-3">
          {title}
          {showWarning && <WarningIcon className="size-4" />}
        </span>
        <ChevronDown
          className={`h-5 w-5 text-[#696969] transform transition-transform duration-300 ease-in-out ${
            isOpen ? "rotate-[180deg]" : "rotate-0"
          }`}
        />
      </CollapsibleTrigger>

      <CollapsibleContent className="overflow-hidden border-t transition-all duration-500 ease-in-out CollapsibleContent">
        <div
          className={`transition-all duration-500 ease-in-out ${
            isOpen ? "opacity-100 py-5" : "max-h-0 opacity-0 py-0"
          } px-5`}
        >
          {children}
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
}
