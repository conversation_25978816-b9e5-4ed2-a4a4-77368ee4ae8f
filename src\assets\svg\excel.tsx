import React from "react";

interface ExcelIconProps {
  color?: string;
  size?: number;
  className?: string;
}

const ExcelIcon = ({
  color = "black",
  size = 18,
  className,
}: ExcelIconProps): React.JSX.Element => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 18 18"
    fill="none"
    className={className}
  >
    <path
      d="M2.14427 2.15767L11.5718 0.811417C11.625 0.803782 11.6792 0.807683 11.7308 0.82284C11.7824 0.837998 11.8301 0.86406 11.8706 0.899265C11.9113 0.934478 11.9438 0.978 11.9662 1.0269C11.9885 1.07579 12 1.12891 12 1.18267V16.8172C12 16.8708 11.9885 16.9239 11.9662 16.9727C11.9439 17.0216 11.9114 17.0651 11.8709 17.1002C11.8304 17.1354 11.7827 17.1615 11.7313 17.1767C11.6798 17.192 11.6257 17.1959 11.5725 17.1884L2.14352 15.8422C1.96473 15.8167 1.80114 15.7276 1.68279 15.5912C1.56444 15.4548 1.49928 15.2803 1.49927 15.0997V2.90017C1.49928 2.71958 1.56444 2.54505 1.68279 2.40865C1.80114 2.27225 1.96548 2.18313 2.14427 2.15767ZM3.00002 3.55117V14.4487L10.5 15.5204V2.47942L3.00002 3.55117ZM12.75 14.2499H15V3.74992H12.75V2.24992H15.75C15.9489 2.24992 16.1397 2.32894 16.2803 2.46959C16.421 2.61024 16.5 2.801 16.5 2.99992V14.9999C16.5 15.1988 16.421 15.3896 16.2803 15.5303C16.1397 15.6709 15.9489 15.7499 15.75 15.7499H12.75V14.2499ZM7.65 8.99994L9.75 11.9999H7.95L6.75002 10.2854L5.55002 11.9999H3.75002L5.85002 8.99994L3.75002 5.99992H5.55002L6.75002 7.71444L7.95 5.99992H9.75L7.65 8.99994Z"
      fill={color}
    />
  </svg>
);

export default ExcelIcon;
