import React from "react";
import { cn } from "../../lib/utils";
import { But<PERSON> } from "../ui/button";
import Delete from "../../assets/svg/Delete";
import Edit from "../../assets/svg/Edit";
export const FormTable = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLTableElement>) => (
  <table
    className={cn("min-w-max w-full overflow-x-scroll", className)}
    {...props}
  />
);

export const FormTableRow = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLTableRowElement>) => (
  <tr className={cn("h-10", className)} {...props} />
);

export const FormTableHeader = ({
  className,
  ...props
}: React.ThHTMLAttributes<HTMLTableCellElement>) => (
  <th
    className={cn(
      "text-xs text-start px-1 border-b border-[#CBD5E1]",
      className
    )}
    {...props}
  />
);

export const FormTableCell = ({
  className,
  ...props
}: React.TdHTMLAttributes<HTMLTableCellElement>) => (
  <td className={cn("px-1 py-1.5 text-xs", className)} {...props} />
);

export const FormTableDeleteCell = ({
  className,
  ...props
}: React.TdHTMLAttributes<HTMLTableCellElement>) => (
  <td className={cn("w-[10px] py-1.5", className)} {...props}>
    <Button
      variant="ghost"
      size="icon"
      onClick={() => props.onClick}
      className="hover:bg-transparent"
    >
      <Delete />
    </Button>
  </td>
);

export const FormTableCard = ({
  className,
  children,
  onDelete,
  onEdit,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  onDelete?: () => void;
  onEdit?: () => void;
}) => (
  <div
    className={cn(
      "flex flex-row justify-between items-center py-2.5 px-3 border w-full border-[#D8D8D8] rounded-lg",
      className
    )}
    {...props}
  >
    <div className="flex flex-col gap-1.5">{children}</div>
    <div className="flex gap-4">
      {onEdit && (
        <Button
          variant="ghost"
          size="icon"
          className="hover:bg-transparent shadow-none w-3 h-2"
          onClick={onEdit}
        >
          <Edit color="#1560bd" />
        </Button>
      )}
      {onDelete && (
        <Button
          variant="ghost"
          size="icon"
          className="hover:bg-transparent shadow-none w-3 h-2"
          onClick={onDelete}
        >
          <Delete />
        </Button>
      )}
    </div>
  </div>
);

export const FormTableCardHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <span className={cn("font-medium text-sm", className)} {...props} />
);

export const FormTableCardRow = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <span className={cn("text-sm text-[#888888]", className)} {...props} />
);
