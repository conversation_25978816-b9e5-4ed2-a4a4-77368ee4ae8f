import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { CheckBoxGroup } from "../components/design-system/check-box";

const meta: Meta<typeof CheckBoxGroup> = {
  title: "Design System/CheckBoxGroup",
  component: CheckBoxGroup,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: { control: "text" },
    options: { control: "object" },
    selectedOptions: { control: "object" },
    onCheckedChange: { action: "changed" },
  },
};

export default meta;
type Story = StoryObj<typeof CheckBoxGroup>;

export const StatusGroup: Story = {
  args: {
    label: "Status",
    options: ["Active", "Inactive"],
    selectedOptions: ["Active"],
    onCheckedChange: (option: string) => (checked: boolean) => {
      console.log(option, checked);
    },
  },
};

export const GroupWithoutLabel: Story = {
  args: {
    options: ["Option 1", "Option 2", "Option 3"],
    selectedOptions: ["Option 2"],
    onCheckedChange: (option: string) => (checked: boolean) => {
      console.log(`Option ${option} changed to ${checked}`);
    },
  },
};
