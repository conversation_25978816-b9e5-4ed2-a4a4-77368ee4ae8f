# Azure Artifacts npm registry configuration
# This file is used as a template for the pipeline

# Set the registry for all packages
registry=https://pkgs.dev.azure.com/cpsAndrew/_packaging/cps-ui-dev/npm/registry/

# Set the registry for scoped packages (if you want to scope your package)
@cps:registry=https://pkgs.dev.azure.com/cpsAndrew/_packaging/cps-ui-dev/npm/registry/

# Always authenticate
always-auth=true

# Note: The actual authentication token will be injected by the Azure DevOps pipeline
# Do not commit actual tokens to source control
