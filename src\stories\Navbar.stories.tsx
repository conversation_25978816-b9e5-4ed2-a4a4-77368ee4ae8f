import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Navbar from "../components/design-system/navbar";

const meta: Meta<typeof Navbar> = {
  title: "Design System/Navbar",
  component: Navbar,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
  argTypes: {
    title: { control: "text" },
    enableBackButton: { control: "boolean" },
    primaryButton: { control: "text" },
    secondaryButton: { control: "text" },
    onBackButtonClick: { action: "back button clicked" },
    onPrimaryButtonClick: { action: "primary button clicked" },
    onSecondaryButtonClick: { action: "secondary button clicked" },
  },
};

export default meta;
type Story = StoryObj<typeof Navbar>;

export const Default: Story = {
  args: {
    title: "Page Title",
  },
};

export const WithBackButton: Story = {
  args: {
    title: "Page with Back",
    enableBackButton: true,
  },
};

export const WithPrimaryButton: Story = {
  args: {
    title: "Page with Primary Action",
    primaryButton: "Save",
  },
};

export const WithSecondaryButton: Story = {
  args: {
    title: "Page with Secondary Action",
    secondaryButton: "Delete",
  },
};

export const Complete: Story = {
  args: {
    title: "Complete Navbar",
    enableBackButton: true,
    primaryButton: "Save Changes",
    secondaryButton: "Delete",
  },
};

export const CustomButtons: Story = {
  args: {
    title: "Custom Button Content",
    primaryButton: (
      <div className="flex items-center gap-2">
        <span>Save</span>
        <span className="text-xs">(Ctrl + S)</span>
      </div>
    ),
    secondaryButton: (
      <div className="flex items-center gap-2">
        <span>Delete</span>
        <span className="text-xs">(Del)</span>
      </div>
    ),
  },
};
