import React from "react";

interface FacilitiesProps {
  className?: string;
  color?: string;
  size?: number;
}
const Facilities = ({
  className,
  color = "white",
  size = 18,
}: FacilitiesProps): React.JSX.Element => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M1.5 16.5H16.5"
      stroke={color}
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.21249 16.5L2.25 7.47749C2.25 7.01999 2.46749 6.58503 2.82749 6.30003L8.07749 2.21252C8.61749 1.79252 9.375 1.79252 9.9225 2.21252L15.1725 6.29252C15.54 6.57752 15.75 7.01249 15.75 7.47749V16.5"
      stroke={color}
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinejoin="round"
    />
    <path
      d="M11.625 8.25H6.375C5.7525 8.25 5.25 8.7525 5.25 9.375V16.5H12.75V9.375C12.75 8.7525 12.2475 8.25 11.625 8.25Z"
      stroke={color}
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.5 12.1875V13.3125"
      stroke={color}
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.875 5.625H10.125"
      stroke={color}
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default Facilities;
