# PowerShell script to publish the package locally for testing
# This script helps test the publishing process before running the full pipeline

param(
    [string]$FeedUrl = "https://pkgs.dev.azure.com/cpsAndrew/_packaging/cps-ui-dev/npm/registry/",
    [switch]$DryRun = $false
)

Write-Host "CPS Common UI - Local Publish Script" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Check if we're in the right directory
if (-not (Test-Path "package.json")) {
    Write-Error "package.json not found. Please run this script from the project root."
    exit 1
}

# Check if pnpm is installed
try {
    $pnpmVersion = pnpm --version
    Write-Host "Using pnpm version: $pnpmVersion" -ForegroundColor Blue
} catch {
    Write-Error "pnpm is not installed. Please install pnpm first: npm install -g pnpm"
    exit 1
}

# Install dependencies
Write-Host "`nInstalling dependencies..." -ForegroundColor Yellow
pnpm install

if ($LASTEXITCODE -ne 0) {
    Write-Error "Failed to install dependencies"
    exit 1
}

# Build the project
Write-Host "`nBuilding the project..." -ForegroundColor Yellow
pnpm run build

if ($LASTEXITCODE -ne 0) {
    Write-Error "Build failed"
    exit 1
}

# Check if dist folder exists
if (-not (Test-Path "dist")) {
    Write-Error "Build output (dist folder) not found"
    exit 1
}

Write-Host "`nBuild completed successfully!" -ForegroundColor Green

# Configure npm registry
Write-Host "`nConfiguring npm registry..." -ForegroundColor Yellow
npm config set registry $FeedUrl
npm config set always-auth true

# Show current configuration
Write-Host "`nCurrent npm configuration:" -ForegroundColor Blue
npm config get registry
npm config get always-auth

if ($DryRun) {
    Write-Host "`nDry run mode - would publish with the following configuration:" -ForegroundColor Cyan
    Write-Host "Registry: $FeedUrl" -ForegroundColor Cyan
    Write-Host "Package: $(Get-Content package.json | ConvertFrom-Json | Select-Object -ExpandProperty name)" -ForegroundColor Cyan
    Write-Host "Version: $(Get-Content package.json | ConvertFrom-Json | Select-Object -ExpandProperty version)" -ForegroundColor Cyan
    Write-Host "`nTo actually publish, run without -DryRun flag" -ForegroundColor Cyan
} else {
    # Publish the package
    Write-Host "`nPublishing to Azure Artifacts..." -ForegroundColor Yellow
    Write-Host "You may be prompted to authenticate with Azure DevOps" -ForegroundColor Blue
    
    npm publish
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`nPackage published successfully!" -ForegroundColor Green
        $packageInfo = Get-Content package.json | ConvertFrom-Json
        Write-Host "Package: $($packageInfo.name)@$($packageInfo.version)" -ForegroundColor Green
    } else {
        Write-Error "Failed to publish package"
        exit 1
    }
}

Write-Host "`nScript completed!" -ForegroundColor Green
