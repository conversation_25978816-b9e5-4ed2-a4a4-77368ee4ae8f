import React, { forwardRef, useState } from "react";
import { Input as ShadcnInput } from "../ui/input";
import { Eye, EyeOff } from "lucide-react";
import { cn } from "../../lib/utils";

export interface PasswordInputProps
  extends React.ComponentProps<typeof ShadcnInput> {
  error?: boolean;
  helperText?: string;
  label?: string;
  required?: boolean;
}

export const PasswordInput = forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ error, className, id, helperText, label, required, ...props }, ref) => {
    const [showPassword, setShowPassword] = useState(false);
    const inputId = id || props.name;

    return (
      <div className="flex flex-col gap-1.25">
        {label && (
          <span className="text-xs">
            {label}
            <span className="ml-0.25">
              {required && <span className="text-destructive">*</span>}
            </span>
          </span>
        )}
        <div className="relative">
          <ShadcnInput
            id={inputId}
            ref={ref}
            type={showPassword ? "text" : "password"}
            className={cn(
              className,
              "!text-xs",
              error
                ? "border-destructive focus:border-destructive focus:ring-destructive"
                : ""
            )}
            {...props}
          />
          <button
            aria-label={showPassword ? "Hide password" : "Show password"}
            onClick={() => setShowPassword((v) => !v)}
            className="absolute inset-y-0 right-2 flex items-center justify-center cursor-pointer"
          >
            {showPassword ? (
              <EyeOff className="h-5 w-5 text-gray-500 hover:text-gray-700" />
            ) : (
              <Eye className="h-5 w-5 text-gray-500 hover:text-gray-700" />
            )}
          </button>
        </div>
        {helperText && <p className="text-xs text-destructive">{helperText}</p>}
      </div>
    );
  }
);

PasswordInput.displayName = "PasswordInput";
