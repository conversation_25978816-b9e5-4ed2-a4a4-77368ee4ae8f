import React from "react";

interface SearchProps {
  className?: string;
  size?: number;
  color?: string;
}

const Search = ({
  className,
  size,
  color = "#7f7f7f",
}: SearchProps): React.JSX.Element => {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.428 10.4868L9.26591 8.32477C9.92116 7.46698 10.2805 6.42653 10.2807 5.32863C10.2807 4.00555 9.76538 2.76158 8.82967 1.82603C7.89412 0.890483 6.65031 0.375183 5.32707 0.375183C4.00399 0.375183 2.76001 0.890483 1.82446 1.82603C-0.106852 3.75751 -0.106852 6.90009 1.82446 8.83124C2.76001 9.76695 4.00399 10.2822 5.32707 10.2822C6.42497 10.2821 7.46541 9.92273 8.3232 9.26748L10.4853 11.4295C10.6153 11.5598 10.7861 11.6249 10.9566 11.6249C11.1272 11.6249 11.2979 11.5598 11.428 11.4295C11.6884 11.1693 11.6884 10.7471 11.428 10.4868ZM2.76717 7.88853C1.35571 6.47707 1.35588 4.18036 2.76717 2.76874C3.45093 2.08514 4.36011 1.70852 5.32707 1.70852C6.29419 1.70852 7.20321 2.08514 7.88696 2.76874C8.57072 3.45249 8.94735 4.36167 8.94735 5.32863C8.94735 6.29576 8.57072 7.20477 7.88696 7.88853C7.20321 8.57229 6.29419 8.94891 5.32707 8.94891C4.36011 8.94891 3.45093 8.57229 2.76717 7.88853Z"
        fill={color}
      />
    </svg>
  );
};

export default Search;
