
# Simple pipeline for CPS Common UI - Build and Publish to Azure Artifacts

trigger:
  branches:
    include:
      - develop

variables:
  nodeVersion: '18.x'
  artifactFeed: 'cps-ui-dev'

pool:
  name: 'CPSAPP'

jobs:
- job: BuildAndPublish
  displayName: 'Build and Publish to Azure Artifacts'
  steps:

  # Setup Node.js
  - task: NodeTool@0
    displayName: 'Setup Node.js'
    inputs:
      versionSpec: $(nodeVersion)

  # Install pnpm
  - script: npm install -g pnpm
    displayName: 'Install pnpm'

  - task: NpmAuthenticate@0
    inputs:
      workingFile: '$(Build.SourcesDirectory)/.npmrc'  

  # Install dependencies
  - script: pnpm install --frozen-lockfile
    displayName: 'Install dependencies'

  # Configure npm registry for Azure Artifacts
  # - script: |
  #     npm config set registry https://pkgs.dev.azure.com/cpsAv/_packaging/$(artifactFeed)/npm/registry/
  #     npm config set always-auth true
  #   displayName: 'Configure npm registry'

  # Authenticate with Azure Artifacts
  # - task: npmAuthenticate@0
  #   displayName: 'Authenticate with Azure Artifacts'
  #   inputs:
  #     workingFile: '.npmrc'

  # Run the release script (builds, versions, and publishes)
  - script: pnpm run release:patch
    displayName: 'Build and Publish Package'
