
# Simple pipeline for CPS Common UI - Build and Publish to Azure Artifacts

trigger:
  branches:
    include:
      - develop

variables:
  nodeVersion: '18.x'
  artifactFeed: 'cps-ui-dev'

pool:
  name: 'CPSAPP'

jobs:
- job: BuildAndPublish
  displayName: 'Build and Publish to Azure Artifacts'
  steps:

  # Setup Node.js
  - task: NodeTool@0
    displayName: 'Setup Node.js'
    inputs:
      versionSpec: $(nodeVersion)

  # Install pnpm
  - script: npm install -g pnpm
    displayName: 'Install pnpm'

  # Configure npm registry for Azure Artifacts
  - script: |
      npm config set registry https://pkgs.dev.azure.com/cpsAv/_packaging/$(artifactFeed)/npm/registry/
      npm config set always-auth true
    displayName: 'Configure npm registry'

  - task: NpmAuthenticate@0
    inputs:
      workingFile: '$(Build.SourcesDirectory)/.npmrc'

  # Install dependencies
  - script: pnpm install
    displayName: 'Install dependencies'

  # Build the library
  - script: pnpm run build
    displayName: 'Build library'

  # Version and publish with proper increment from published version
  - powershell: |
      Write-Host "Current version in package.json:"
      $currentVersion = node -p "require('./package.json').version"
      Write-Host $currentVersion

      Write-Host "Checking latest published version from $(artifactFeed) feed..."
      try {
          $latestVersion = npm view common-ui version 2>$null
          if (-not $latestVersion) { $latestVersion = "0.0.22" }
      } catch {
          $latestVersion = "0.0.22"
      }
      Write-Host "Latest published version: $latestVersion"

      # Extract version parts and increment patch
      $versionParts = $latestVersion.Split('.')
      $major = [int]$versionParts[0]
      $minor = [int]$versionParts[1]
      $patch = [int]$versionParts[2]
      $newPatch = $patch + 1
      $newVersion = "$major.$minor.$newPatch"

      Write-Host "Setting new version to: $newVersion"
      npm version $newVersion --no-git-tag-version

      Write-Host "Publishing to $(artifactFeed) feed..."
      npm publish
    displayName: 'Version and Publish Package'
