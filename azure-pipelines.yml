
# Simple pipeline for CPS Common UI - Build and Publish to Azure Artifacts

trigger:
  branches:
    include:
      - develop

variables:
  nodeVersion: '18.x'
  artifactFeed: 'cps-ui-dev'

pool:
  name: 'CPSAPP'

jobs:
- job: BuildAndPublish
  displayName: 'Build and Publish to Azure Artifacts'
  steps:

  # Setup Node.js
  - task: NodeTool@0
    displayName: 'Setup Node.js'
    inputs:
      versionSpec: $(nodeVersion)

  # Install pnpm
  - script: npm install -g pnpm
    displayName: 'Install pnpm'

  - task: NpmAuthenticate@0
    inputs:
      workingFile: '$(Build.SourcesDirectory)/.npmrc'  

  # Install dependencies
  - script: pnpm install
    displayName: 'Install dependencies'

  # Build the library
  - script: pnpm run build
    displayName: 'Build library'

  # Version and publish (skip git operations to avoid "working directory not clean" error)
  - script: |
      npm version patch --no-git-tag-version
      npm publish
    displayName: 'Version and Publish Package'
