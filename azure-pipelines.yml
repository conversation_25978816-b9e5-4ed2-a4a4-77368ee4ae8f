
trigger:
  branches:
    include:
      - develop
 
variables:
  # Build configuration
  buildConfiguration: 'Release'
  nodeVersion: '18.x'
  
  # Azure Artifacts feed configuration
  artifactFeed: 'cps-ui-dev'
  
  # Release type (patch, minor, major)
  releaseType: 'patch'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'Build and Test'
  jobs:
  - job: BuildJob
    displayName: 'Build Job'
    steps:
    
    # Setup Node.js
    - task: NodeTool@0
      displayName: 'Setup Node.js'
      inputs:
        versionSpec: $(nodeVersion)
        checkLatest: true

    # Cache node_modules for faster builds
    - task: Cache@2
      displayName: 'Cache node_modules'
      inputs:
        key: 'pnpm | "$(Agent.OS)" | pnpm-lock.yaml'
        restoreKeys: |
          pnpm | "$(Agent.OS)"
        path: $(Pipeline.Workspace)/.pnpm-store

    # Install pnpm
    - script: |
        npm install -g pnpm
        pnpm config set store-dir $(Pipeline.Workspace)/.pnpm-store
      displayName: 'Install pnpm'

    # Install dependencies
    - script: pnpm install --frozen-lockfile
      displayName: 'Install dependencies'


    # Build the library
    - script: pnpm run build
      displayName: 'Build library'

    # Create build artifacts
    - task: CopyFiles@2
      displayName: 'Copy build artifacts'
      inputs:
        sourceFolder: '$(Build.SourcesDirectory)'
        contents: |
          dist/**
          package.json
          README.md
          LICENSE
        targetFolder: '$(Build.ArtifactStagingDirectory)'

    # Publish build artifacts
    - task: PublishBuildArtifacts@1
      displayName: 'Publish build artifacts'
      inputs:
        pathToPublish: '$(Build.ArtifactStagingDirectory)'
        artifactName: 'npm-package'
        publishLocation: 'Container'

- stage: ConfigureNpmRegistry
  displayName: 'Configure npm for Azure Artifacts'
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  jobs:
  - job: ConfigureRegistry
    displayName: 'Setup npm registry for Azure Artifacts'
    steps:

    # Setup Node.js
    - task: NodeTool@0
      displayName: 'Setup Node.js'
      inputs:
        versionSpec: $(nodeVersion)

    # Install pnpm
    - script: npm install -g pnpm
      displayName: 'Install pnpm'

    # Install dependencies
    - script: pnpm install --frozen-lockfile
      displayName: 'Install dependencies'

    # Configure npm registry for Azure Artifacts
    - script: |
        echo "Configuring npm registry for Azure Artifacts..."
        npm config set registry https://pkgs.dev.azure.com/cpsAndrew/_packaging/$(artifactFeed)/npm/registry/
        npm config set always-auth true
        echo "@cps:registry=https://pkgs.dev.azure.com/cpsAndrew/_packaging/$(artifactFeed)/npm/registry/" >> .npmrc
        echo "always-auth=true" >> .npmrc
      displayName: 'Configure npm registry'

    # Authenticate with Azure Artifacts
    - task: npmAuthenticate@0
      displayName: 'Authenticate with Azure Artifacts'
      inputs:
        workingFile: '.npmrc'

    # Run the release script (this will build, version, and publish)
    - script: |
        echo "Running release script..."
        pnpm run release:$(releaseType)
      displayName: 'Release to Azure Artifacts'
      env:
        NPM_TOKEN: $(System.AccessToken)
