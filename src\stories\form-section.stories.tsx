import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import FormSection from "../components/design-system/form-section";

const meta: Meta<typeof FormSection> = {
  title: "Design System/FormSection",
  component: FormSection,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    title: { control: "text" },
    onAddNew: { action: "add new" },
  },
};

export default meta;
type Story = StoryObj<typeof FormSection>;

export const Default: Story = {
  args: {
    title: "Section Title",
    children: (
      <div className="p-4">
        <p>This is the content of the form section.</p>
      </div>
    ),
  },
  render: (args) => {
    return <FormSection title="Section Title">{args.children}</FormSection>;
  },
};

export const WithAddNew: Story = {
  args: {
    title: "Section with Add New",
    children: (
      <div className="p-4">
        <p>This section has an "Add New" button.</p>
      </div>
    ),
    onAddNew: () => console.log("Add new clicked"),
  },
};

export const WithFormContent: Story = {
  args: {
    title: "Form Fields Section",
    children: (
      <div className="space-y-4 p-4">
        <div className="flex gap-4">
          <div className="flex-1">
            <label className="text-sm">Field 1</label>
            <input type="text" className="w-full border rounded p-2" />
          </div>
          <div className="flex-1">
            <label className="text-sm">Field 2</label>
            <input type="text" className="w-full border rounded p-2" />
          </div>
        </div>
      </div>
    ),
  },
};
