import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { MultiSelectDropdown } from "../components/design-system/multi-select-dropdown";

const meta: Meta<typeof MultiSelectDropdown> = {
  title: "Design System/MultiSelectDropdown",
  component: MultiSelectDropdown,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: { control: "text" },
    placeholder: { control: "text" },
    maxDisplayItems: { control: "number" },
    required: { control: "boolean" },
    selectAll: { control: "boolean" },
    onChange: { action: "changed" },
    helperText: { control: "text" },
  },
};

export default meta;
type Story = StoryObj<typeof MultiSelectDropdown>;

const options = [
  { value: "option1", label: "Option 1" },
  { value: "option2", label: "Option 2" },
  { value: "option3", label: "Option 3" },
  { value: "option4", label: "Option 4" },
  { value: "option5", label: "Option 5" },
];

export const Default: Story = {
  args: {
    label: "Select Options",
    options,
    selected: [],
    placeholder: "Choose options",
  },
};

export const WithSelections: Story = {
  args: {
    label: "Selected Options",
    options,
    selected: ["option1", "option3"],
  },
};

export const Required: Story = {
  args: {
    label: "Required Selection",
    options,
    selected: [],
    required: true,
  },
};

export const WithWarning: Story = {
  args: {
    label: "Selection with Warning",
    options,
    selected: [],
    helperText: "Required Field",
  },
};

export const WithoutSelectAll: Story = {
  args: {
    label: "Without Select All",
    options,
    selected: [],
    selectAll: false,
  },
};

export const LimitedDisplay: Story = {
  args: {
    label: "Limited Display",
    options,
    selected: ["option1", "option2", "option3", "option4"],
    maxDisplayItems: 2,
  },
};
