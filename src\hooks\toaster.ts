import Toaster from "../components/design-system/toaster";
import { toast } from "react-toastify";

type ToastType = "success" | "warning" | "error" | "info";

const createToast = (type: ToastType) => (content?: string) => {
  const titleMap: Record<ToastType, string> = {
    success: "Success Message",
    warning: "Warning Message",
    error: "Error Message",
    info: "Information Message",
  };

  toast(Toaster, {
    data: { type, title: titleMap[type], content },
    closeButton: false,
  });
};

export const toaster = {
  success: createToast("success"),
  warning: createToast("warning"),
  error: createToast("error"),
  info: createToast("info"),
};
