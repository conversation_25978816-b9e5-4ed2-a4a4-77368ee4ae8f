import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import LoadingSpinner from "../components/ui/loading-spinner";

const meta: Meta<typeof LoadingSpinner> = {
  title: "Design System/LoadingSpinner",
  component: LoadingSpinner,
  tags: ["autodocs"],
  argTypes: {
    size: { control: "number" },
    dots: { control: "number" },
  },
};

export default meta;
type Story = StoryObj<typeof LoadingSpinner>;

export const Default: Story = {
  args: {
    size: 58,
    dots: 10,
  },
};
