import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { PaginatedSearchMultiSelectDropdown } from "../components/design-system/paginated-search-multi-select-dropdown";

const meta: Meta<typeof PaginatedSearchMultiSelectDropdown> = {
  title: "Design System/PaginatedSearchMultiSelectDropdown",
  component: PaginatedSearchMultiSelectDropdown,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: { control: "text" },
    options: { control: "object" },
    selected: { control: "object" },
    onChange: { action: "changed" },
    placeholder: { control: "text" },
    maxDisplayItems: { control: "number" },
    required: { control: "boolean" },
    onSearch: { action: "searched" },
    isLoading: { control: "boolean" },
  },
};

export default meta;
type Story = StoryObj<typeof PaginatedSearchMultiSelectDropdown>;

const sampleOptions = [
  { label: "Option 1", value: "1" },
  { label: "Option 2", value: "2" },
  { label: "Option 3", value: "3" },
  { label: "Option 4", value: "4" },
  { label: "Option 5", value: "5" },
];

export const Default: Story = {
  args: {
    label: "Select Options",
    options: sampleOptions,
    selected: [],
    placeholder: "Select items",
    maxDisplayItems: 3,
    required: false,
    isLoading: false,
  },
};

export const WithSelectedItems: Story = {
  args: {
    label: "Select Options",
    options: sampleOptions,
    selected: [
      { label: "Option 1", value: "1" },
      { label: "Option 2", value: "2" },
    ],
    placeholder: "Select items",
    maxDisplayItems: 3,
    required: false,
    isLoading: false,
  },
};

export const Required: Story = {
  args: {
    label: "Select Options",
    options: sampleOptions,
    selected: [],
    placeholder: "Select items",
    maxDisplayItems: 3,
    required: true,
    isLoading: false,
  },
};

export const Loading: Story = {
  args: {
    label: "Select Options",
    options: sampleOptions,
    selected: [],
    placeholder: "Select items",
    maxDisplayItems: 3,
    required: false,
    isLoading: true,
  },
};

export const CustomMaxDisplayItems: Story = {
  args: {
    label: "Select Options",
    options: sampleOptions,
    selected: [
      { label: "Option 1", value: "1" },
      { label: "Option 2", value: "2" },
      { label: "Option 3", value: "3" },
      { label: "Option 4", value: "4" },
    ],
    placeholder: "Select items",
    maxDisplayItems: 2,
    required: false,
    isLoading: false,
  },
}; 