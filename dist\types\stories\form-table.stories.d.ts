import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { FormTable } from "../components/design-system/form-table";
declare const meta: Meta<typeof FormTable>;
export default meta;
type Story = StoryObj<typeof FormTable>;
export declare const Default: Story;
export declare const WithCustomStyling: Story;
export declare const WithInputs: Story;
//# sourceMappingURL=form-table.stories.d.ts.map