import * as React from "react";

interface ThemeDaysProps {
  className?: string;
  color?: string;
  size?: number;
}
const ThemeDays = ({
  className,
  color = "black",
  size = 18,
}: ThemeDaysProps): React.JSX.Element => (
  <svg
    className={className}
    width={size}
    height={size}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.75 16.5H11.25C15 16.5 16.5 15 16.5 11.25V6.75C16.5 3 15 1.5 11.25 1.5H6.75C3 1.5 1.5 3 1.5 6.75V11.25C1.5 15 3 16.5 6.75 16.5Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.25 6.1875C6 6.9375 7.2225 6.9375 7.98 6.1875"
      stroke={color}
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.02 6.1875C10.77 6.9375 11.9925 6.9375 12.75 6.1875"
      stroke={color}
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.3 9.75H11.7C12.075 9.75 12.375 10.05 12.375 10.425C12.375 12.2925 10.8675 13.8 9 13.8C7.1325 13.8 5.625 12.2925 5.625 10.425C5.625 10.05 5.925 9.75 6.3 9.75Z"
      stroke={color}
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default ThemeDays;
