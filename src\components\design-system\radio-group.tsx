import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { cn } from "../../lib/utils";
import * as RadioGroupPrimitive from "@radix-ui/react-radio-group";

function RadioItem({
  children,
  className,
  ...props
}: React.ComponentProps<typeof RadioGroupPrimitive.Item>) {
  return (
    <label
      htmlFor={props.value}
      className="flex items-center gap-2 cursor-pointer"
    >
      <RadioGroupItem
        id={props.value}
        className={cn(
          "data-[state=checked]:border-transparent data-[state=checked]:bg-secondary",
          className
        )}
        {...props}
      />
      <span className="text-sm font-medium">{children}</span>
    </label>
  );
}
export { RadioItem, RadioGroup as RadioItemGroup };
