import type { Table } from "@tanstack/react-table";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";
import { Button } from "../ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";

interface DataTablePaginationProps<TData> {
  table: Table<TData>;
  pageSizeOptions: number[];
  onPaginationChange?: (skip: number, limit: number) => void;
  totalCount?: number;
  isLoading?: boolean;
  text: {
    showText: string;
    totalCountText: string;
    loadingText: string;
    pageText: string;
    ofText: string;
    goToFirstPageText: string;
    goToPreviousPageText: string;
    goToNextPageText: string;
    goToLastPageText: string;
  };
}

export function DataTablePagination<TData>({
  table,
  onPaginationChange,
  totalCount,
  isLoading,
  text,
  pageSizeOptions,
}: DataTablePaginationProps<TData>) {
  return (
    <div className="flex items-center justify-between p-2">
      <div className="flex items-center space-x-2">
        <p className="text-sm">{text.showText}</p>
        <Select
          value={`${table.getState().pagination.pageSize}`}
          onValueChange={(value) => {
            const newPageSize = Number(value);
            const currentPageIndex = table.getState().pagination.pageIndex;
            const targetPageIndex = 0;
            const targetSkip = 0;
            if (currentPageIndex !== 0) {
              table.setPageIndex(targetPageIndex);
            }
            table.setPageSize(newPageSize);

            if (onPaginationChange) {
              onPaginationChange(targetSkip, newPageSize);
            }
          }}
        >
          <SelectTrigger className="h-6 w-[4rem] rounded-full border border-input px-2 py-0 text-sm">
            <SelectValue
              placeholder={`${table.getState().pagination.pageSize}`}
            />
          </SelectTrigger>
          <SelectContent side="top">
            {pageSizeOptions?.map((pageSize) => (
              <SelectItem
                key={pageSize}
                value={`${pageSize}`}
                className="cursor-pointer py-1.5 px-2 text-sm data-[state=checked]:bg-primary data-[state=checked]:text-white rounded-none"
              >
                {pageSize}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <p className="text-sm">{text.totalCountText}</p>
      </div>
      <div className="flex items-center space-x-3 lg:space-x-3">
        <div className="flex w-[6.25rem] items-center justify-end text-sm">
          {isLoading ? (
            <>{text.loadingText}</>
          ) : typeof totalCount === "number" ? (
            totalCount === 0 ? (
              <>
                {text.pageText} 0 {text.ofText} 0
              </>
            ) : (
              <>
                {text.pageText} {table.getState().pagination.pageIndex + 1}{" "}
                {text.ofText}{" "}
                {Math.ceil(totalCount / table.getState().pagination.pageSize)}
              </>
            )
          ) : (
            <>{text.loadingText}</>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex rounded-4xl hover:text-black"
            onClick={() => {
              table.setPageIndex(0);
              if (onPaginationChange) {
                onPaginationChange(0, table.getState().pagination.pageSize);
              }
            }}
            disabled={!table.getCanPreviousPage()}
          >
            <span className="sr-only">{text.goToFirstPageText}</span>
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0 rounded-4xl hover:text-black"
            onClick={() => {
              const prevPageIndex = table.getState().pagination.pageIndex - 1;
              table.previousPage();
              if (onPaginationChange) {
                onPaginationChange(
                  prevPageIndex * table.getState().pagination.pageSize,
                  table.getState().pagination.pageSize
                );
              }
            }}
            disabled={!table.getCanPreviousPage()}
          >
            <span className="sr-only">{text.goToPreviousPageText}</span>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0 rounded-4xl hover:text-black"
            onClick={() => {
              const nextPageIndex = table.getState().pagination.pageIndex + 1;
              table.nextPage();
              if (onPaginationChange) {
                onPaginationChange(
                  nextPageIndex * table.getState().pagination.pageSize,
                  table.getState().pagination.pageSize
                );
              }
            }}
            disabled={!table.getCanNextPage()}
          >
            <span className="sr-only">{text.goToNextPageText}</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex rounded-4xl hover:text-black"
            onClick={() => {
              const lastPageIndex = table.getPageCount() - 1;
              table.setPageIndex(lastPageIndex);
              if (onPaginationChange) {
                onPaginationChange(
                  lastPageIndex * table.getState().pagination.pageSize,
                  table.getState().pagination.pageSize
                );
              }
            }}
            disabled={!table.getCanNextPage()}
          >
            <span className="sr-only">{text.goToLastPageText}</span>
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
