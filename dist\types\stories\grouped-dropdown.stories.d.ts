import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { GroupedDropdown } from "../components/design-system/grouped-dropdown";
declare const meta: Meta<typeof GroupedDropdown>;
export default meta;
type Story = StoryObj<typeof GroupedDropdown>;
export declare const Default: Story;
export declare const WithSelectedItems: Story;
export declare const Required: Story;
export declare const MultipleSelected: Story;
export declare const CustomMaxDisplayItems: Story;
//# sourceMappingURL=grouped-dropdown.stories.d.ts.map