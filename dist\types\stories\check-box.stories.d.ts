import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { <PERSON><PERSON><PERSON><PERSON>ield } from "../components/design-system/check-box";
declare const meta: Meta<typeof CheckBoxField>;
export default meta;
type Story = StoryObj<typeof CheckBoxField>;
export declare const Default: Story;
export declare const Checked: Story;
export declare const Disabled: Story;
export declare const Disabled11: Story;
//# sourceMappingURL=check-box.stories.d.ts.map