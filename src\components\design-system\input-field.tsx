import React, { forwardRef } from "react";
import { Input as ShadcnInput } from "../ui/input";
import { cn } from "../../lib/utils";

export interface InputFieldProps
  extends React.ComponentProps<typeof ShadcnInput> {
  label?: string;
  required?: boolean;
  helperText?: string;
  disabled?: boolean;
}

export const InputField = forwardRef<HTMLInputElement, InputFieldProps>(
  ({ label, required, helperText, disabled, ...props }, ref) => {
    return (
      <div className="flex flex-col gap-1.25">
        {label && (
          <span className="text-xs">
            {label}
            <span className="ml-0.25">
              {required && <span className="text-[#EB062B]">*</span>}
            </span>
          </span>
        )}
        <ShadcnInput
          ref={ref}
          required={required}
          className={cn(
            "!text-xs h-9",
            helperText &&
              "border-[#EB062B] focus-visible:border-[#EB062B] selection:bg-[#EB062B] focus-visible:ring-[#EB062B]/20",
            disabled && "bg-[#EAEEF3]"
          )}
          {...props}
        />
        {helperText && (
          <span className="text-[10px] text-[#EB062B]">{helperText}</span>
        )}
      </div>
    );
  }
);

InputField.displayName = "InputField";
