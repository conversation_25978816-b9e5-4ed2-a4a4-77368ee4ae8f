import { AppSidebar } from "./app-sidebar";
import { SidebarInset, SidebarProvider } from "../design-system/sidebar";
import type { PropsWithChildren } from "react";
import { Header } from "./header";
import { Footer } from "./footer";
import Navbar from "../design-system/navbar";
import type { sidebarApps } from "./app-switcher";
import type { NavMainItem } from "./nav-main";

interface DashboardLayoutProps extends PropsWithChildren {
  enableBackButton?: boolean;
  title?: string;
  primaryButton?: string | React.ReactNode;
  secondaryButton?: string | React.ReactNode;
  onBackButtonClick?: () => void;
  onPrimaryButtonClick?: () => void;
  onSecondaryButtonClick?: () => void;
  scrollbarExists?: boolean;
  searchBar?: boolean;
  primaryButtonLoading?: boolean;
  secondaryButtonLoading?: boolean;
  searchValue?: string;
  onSearchChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  secondaryButtonIcon?: React.ReactNode;
  header: {
    userDropdownItems?: {
      label: string;
      icon?: React.ReactNode;
      onClick?: () => void;
    }[];
    user: string;
    userFallback: string;
    headerImg: string;
    sidebarExpanded: string;
    sidebarCollapsed: string;
  };
  sidebar: {
    apps: sidebarApps[];
    navMain: NavMainItem[];
    footer: React.ReactNode;
    activeApp: sidebarApps;
    setActiveApp: (app: sidebarApps) => void;
    logo: string;
    logoExpanded: string;
    sidebarCollapsed: string;
    sidebarExpanded: string;
  };
}

const DashboardLayoutPrimitive = ({
  enableBackButton,
  title,
  primaryButton,
  secondaryButton,
  onBackButtonClick,
  onPrimaryButtonClick,
  onSecondaryButtonClick,
  scrollbarExists,
  searchBar,
  primaryButtonLoading,
  secondaryButtonLoading,
  searchValue,
  onSearchChange,
  secondaryButtonIcon,
  header,
  sidebar,
  children,
}: DashboardLayoutProps): React.JSX.Element => {
  return (
    <SidebarProvider>
      <AppSidebar
        sidebarApps={sidebar.apps}
        sidebarNavMain={sidebar.navMain}
        sidebarFooter={sidebar.footer}
        activeApp={sidebar.activeApp}
        setActiveApp={sidebar.setActiveApp}
        logo={sidebar.logo}
        logoExpanded={sidebar.logoExpanded}
        sidebarCollapsed={sidebar.sidebarCollapsed}
        sidebarExpanded={sidebar.sidebarExpanded}
      />
      <SidebarInset className="h-screen overflow-hidden">
        <Header
          user={header.user}
          userFallback={header.userFallback}
          userDropdownItems={header.userDropdownItems}
          headerImg={header.headerImg}
          sidebarExpanded={header.sidebarExpanded}
          sidebarCollapsed={header.sidebarCollapsed}
        />
        <Navbar
          title={title}
          enableBackButton={enableBackButton}
          primaryButton={primaryButton}
          secondaryButton={secondaryButton}
          onBackButtonClick={onBackButtonClick}
          onPrimaryButtonClick={onPrimaryButtonClick}
          onSecondaryButtonClick={onSecondaryButtonClick}
          scrollbarExists={scrollbarExists}
          searchBar={searchBar}
          primaryButtonLoading={primaryButtonLoading}
          secondaryButtonLoading={secondaryButtonLoading}
          searchValue={searchValue}
          onSearchChange={onSearchChange}
          secondaryButtonIcon={secondaryButtonIcon}
        />
        <div className="h-[calc(100vh-4rem)] overflow-y-auto flex flex-col bg-[#f1f5f9] px-6 py-2">
          {children}
        </div>
        <Footer />
      </SidebarInset>
    </SidebarProvider>
  );
};

export { DashboardLayoutPrimitive };
