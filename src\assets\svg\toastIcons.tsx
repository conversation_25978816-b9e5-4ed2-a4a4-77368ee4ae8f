import * as React from "react";

interface ToastIconsProps {
  type?: "info" | "warning" | "error" | "success";
  className?: string;
  size?: number;
}

const ToastIcons = ({
  type = "info",
  className,
  size = 18,
}: ToastIconsProps): React.JSX.Element => {
  return (
    <>
      {type === "info" && (
        <svg
          className={className}
          width={size}
          height={size}
          viewBox="0 0 18 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9 16.5C13.1325 16.5 16.5 13.1325 16.5 9C16.5 4.8675 13.1325 1.5 9 1.5C4.8675 1.5 1.5 4.8675 1.5 9C1.5 13.1325 4.8675 16.5 9 16.5ZM9.5625 12C9.5625 12.3075 9.3075 12.5625 9 12.5625C8.6925 12.5625 8.4375 12.3075 8.4375 12L8.4375 8.25C8.4375 7.9425 8.6925 7.6875 9 7.6875C9.3075 7.6875 9.5625 7.9425 9.5625 8.25V12ZM8.31 5.715C8.3475 5.6175 8.4 5.5425 8.4675 5.4675C8.5425 5.4 8.625 5.3475 8.715 5.31C8.805 5.2725 8.9025 5.25 9 5.25C9.0975 5.25 9.195 5.2725 9.285 5.31C9.375 5.3475 9.4575 5.4 9.5325 5.4675C9.6 5.5425 9.6525 5.6175 9.69 5.715C9.7275 5.805 9.75 5.9025 9.75 6C9.75 6.0975 9.7275 6.195 9.69 6.285C9.6525 6.375 9.6 6.4575 9.5325 6.5325C9.4575 6.6 9.375 6.6525 9.285 6.69C9.105 6.765 8.895 6.765 8.715 6.69C8.625 6.6525 8.5425 6.6 8.4675 6.5325C8.4 6.4575 8.3475 6.375 8.31 6.285C8.2725 6.195 8.25 6.0975 8.25 6C8.25 5.9025 8.2725 5.805 8.31 5.715Z"
            fill="#2E86DB"
          />
        </svg>
      )}
      {type === "warning" && (
        <svg
          width="18"
          height="18"
          viewBox="0 0 18 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M14.6329 4.3875L10.1779 1.815C9.45043 1.395 8.55043 1.395 7.81543 1.815L3.36793 4.3875C2.64043 4.8075 2.19043 5.5875 2.19043 6.435V11.565C2.19043 12.405 2.64043 13.185 3.36793 13.6125L7.82293 16.185C8.55043 16.605 9.45043 16.605 10.1854 16.185L14.6404 13.6125C15.3679 13.1925 15.8179 12.4125 15.8179 11.565V6.435C15.8104 5.5875 15.3604 4.815 14.6329 4.3875ZM8.43793 5.8125C8.43793 5.505 8.69293 5.25 9.00043 5.25C9.30793 5.25 9.56293 5.505 9.56293 5.8125V9.75C9.56293 10.0575 9.30793 10.3125 9.00043 10.3125C8.69293 10.3125 8.43793 10.0575 8.43793 9.75V5.8125ZM9.69043 12.4725C9.65293 12.5625 9.60043 12.645 9.53293 12.72C9.39043 12.8625 9.20293 12.9375 9.00043 12.9375C8.90293 12.9375 8.80543 12.915 8.71543 12.8775C8.61793 12.84 8.54293 12.7875 8.46793 12.72C8.40043 12.645 8.34793 12.5625 8.30293 12.4725C8.26543 12.3825 8.25043 12.285 8.25043 12.1875C8.25043 11.9925 8.32543 11.7975 8.46793 11.655C8.54293 11.5875 8.61793 11.535 8.71543 11.4975C8.99293 11.3775 9.32293 11.445 9.53293 11.655C9.60043 11.73 9.65293 11.805 9.69043 11.9025C9.72793 11.9925 9.75043 12.09 9.75043 12.1875C9.75043 12.285 9.72793 12.3825 9.69043 12.4725Z"
            fill="#F6B031"
          />
        </svg>
      )}
      {type === "success" && (
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8 0.5C3.8675 0.5 0.5 3.8675 0.5 8C0.5 12.1325 3.8675 15.5 8 15.5C12.1325 15.5 15.5 12.1325 15.5 8C15.5 3.8675 12.1325 0.5 8 0.5ZM11.585 6.275L7.3325 10.5275C7.2275 10.6325 7.085 10.6925 6.935 10.6925C6.785 10.6925 6.6425 10.6325 6.5375 10.5275L4.415 8.405C4.1975 8.1875 4.1975 7.8275 4.415 7.61C4.6325 7.3925 4.9925 7.3925 5.21 7.61L6.935 9.335L10.79 5.48C11.0075 5.2625 11.3675 5.2625 11.585 5.48C11.8025 5.6975 11.8025 6.05 11.585 6.275Z"
            fill="#15A48A"
          />
        </svg>
      )}
      {type === "error" && (
        <svg
          width="18"
          height="18"
          viewBox="0 0 18 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9 1.5C4.8675 1.5 1.5 4.8675 1.5 9C1.5 13.1325 4.8675 16.5 9 16.5C13.1325 16.5 16.5 13.1325 16.5 9C16.5 4.8675 13.1325 1.5 9 1.5ZM11.52 10.725C11.7375 10.9425 11.7375 11.3025 11.52 11.52C11.4075 11.6325 11.265 11.685 11.1225 11.685C10.98 11.685 10.8375 11.6325 10.725 11.52L9 9.795L7.275 11.52C7.1625 11.6325 7.02 11.685 6.8775 11.685C6.735 11.685 6.5925 11.6325 6.48 11.52C6.2625 11.3025 6.2625 10.9425 6.48 10.725L8.205 9L6.48 7.275C6.2625 7.0575 6.2625 6.6975 6.48 6.48C6.6975 6.2625 7.0575 6.2625 7.275 6.48L9 8.205L10.725 6.48C10.9425 6.2625 11.3025 6.2625 11.52 6.48C11.7375 6.6975 11.7375 7.0575 11.52 7.275L9.795 9L11.52 10.725Z"
            fill="#FE494D"
          />
        </svg>
      )}
    </>
  );
};

export default ToastIcons;
