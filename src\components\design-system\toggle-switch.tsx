import { Switch } from "../ui/switch";
import { Label } from "../ui/label";
import { useId, forwardRef } from "react";
import { cn } from "../../lib/utils";

interface ToggleSwitchProps
  extends Omit<
    React.ButtonHTMLAttributes<HTMLButtonElement>,
    "onChange" | "value" | "type"
  > {
  label?: string;
  value?: boolean;
  onChange?: (checked: boolean) => void;
  required?: boolean;
  option?: string;
  error?: boolean;
  helperText?: string;
  onCheckedChange?: (checked: boolean) => void;
}

const ToggleSwitch = forwardRef<HTMLButtonElement, ToggleSwitchProps>(
  (
    {
      label = "Active",
      onChange,
      value = false,
      required = false,
      option = "active",
      error,
      helperText,
      name,
      className,
      disabled,
      defaultChecked,
      onCheckedChange,
      id: propId,
      ...props
    },
    ref
  ) => {
    const generatedId = useId();
    const id = propId || generatedId;

    return (
      <div className="flex flex-col gap-1.5">
        {label && (
          <span className="text-xs">
            {label}
            <span className="ml-0.25">
              {required && <span className="text-destructive">*</span>}
            </span>
          </span>
        )}
        <div className="flex items-center gap-2 h-9">
          <Switch
            id={id}
            ref={ref}
            checked={value}
            className={cn(
              "data-[state=checked]:bg-[#3498DB]",
              error &&
                "border-destructive focus:border-destructive focus:ring-destructive",
              className
            )}
            onCheckedChange={onChange || onCheckedChange}
            name={name}
            disabled={disabled}
            defaultChecked={defaultChecked}
            {...props}
          />
          <Label htmlFor={id} className="text-xs cursor-pointer font-medium">
            {option}
          </Label>
        </div>
        {error && helperText && (
          <p className="text-xs text-destructive">{helperText}</p>
        )}
      </div>
    );
  }
);

ToggleSwitch.displayName = "ToggleSwitch";

export default ToggleSwitch;
