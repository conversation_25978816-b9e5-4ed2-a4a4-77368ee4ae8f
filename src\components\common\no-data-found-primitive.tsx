import NoDataFoundImg from "../../assets/no-data-found.png";

interface props {
  isNoRecordsFound?: boolean;
  message?: string;
  noDataText: string;
}

const NoDataFoundPrimitive = ({
  isNoRecordsFound,
  message,
  noDataText,
}: props) => {
  return (
    <div className="flex flex-col gap-2 justify-center items-center my-auto h-full">
      <img src={NoDataFoundImg} alt="empty-file-holder" className="mb-3" />
      {isNoRecordsFound && <p className="text-sm font-medium">{noDataText}</p>}
      <p className="text-sm font-medium">{message || noDataText}</p>
    </div>
  );
};

export default NoDataFoundPrimitive;
