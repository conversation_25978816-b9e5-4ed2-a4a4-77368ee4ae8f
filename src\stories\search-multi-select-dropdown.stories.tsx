import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { SearchMultiSelectDropdown } from "../components/design-system/search-multi-select-dropdown";

const meta: Meta<typeof SearchMultiSelectDropdown> = {
  title: "Design System/SearchMultiSelectDropdown",
  component: SearchMultiSelectDropdown,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: { control: "text" },
    options: { control: "object" },
    selected: { control: "object" },
    onChange: { action: "changed" },
    placeholder: { control: "text" },
    maxDisplayItems: { control: "number" },
    required: { control: "boolean" },
  },
};

export default meta;
type Story = StoryObj<typeof SearchMultiSelectDropdown>;

const sampleOptions = [
  { label: "Option 1", value: "1" },
  { label: "Option 2", value: "2" },
  { label: "Option 3", value: "3" },
  { label: "Option 4", value: "4" },
  { label: "Option 5", value: "5" },
];

export const Default: Story = {
  args: {
    label: "Select Options",
    options: sampleOptions,
    selected: [],
    placeholder: "Select items",
    maxDisplayItems: 3,
    required: false,
  },
};

export const WithSelectedItems: Story = {
  args: {
    label: "Select Options",
    options: sampleOptions,
    selected: ["1", "2"],
    placeholder: "Select items",
    maxDisplayItems: 3,
    required: false,
  },
};

export const Required: Story = {
  args: {
    label: "Select Options",
    options: sampleOptions,
    selected: [],
    placeholder: "Select items",
    maxDisplayItems: 3,
    required: true,
  },
};

export const AllSelected: Story = {
  args: {
    label: "Select Options",
    options: sampleOptions,
    selected: ["1", "2", "3", "4", "5"],
    placeholder: "Select items",
    maxDisplayItems: 3,
    required: false,
  },
};

export const CustomMaxDisplayItems: Story = {
  args: {
    label: "Select Options",
    options: sampleOptions,
    selected: ["1", "2", "3", "4"],
    placeholder: "Select items",
    maxDisplayItems: 2,
    required: false,
  },
}; 