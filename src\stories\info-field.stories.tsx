import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { InfoField } from "../components/design-system/info-field";

const meta: Meta<typeof InfoField> = {
  title: "Design System/InfoField",
  component: InfoField,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: { control: "text" }, 
    value: { control: "text" }, 
  },
};

export default meta;
type Story = StoryObj<typeof InfoField>;

export const Default: Story = {
  args: {
    label: "Phone Number",
    value: "************",
  },
};
