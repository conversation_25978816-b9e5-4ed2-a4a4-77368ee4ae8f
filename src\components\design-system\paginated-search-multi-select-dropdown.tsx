import * as React from "react";
import { ChevronDown } from "lucide-react";
import { cn } from "../../lib/utils";
import { Button } from "../ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "../ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Checkbox } from "../ui/check-box";
import InfiniteScrollList from "./infinite-scroll-list";

export type Option = {
  label: string;
  value: string;
};

interface PaginatedSearchMultiSelectProps {
  label?: string;
  options: Option[];
  selected: Option[];
  onChange: (selected: Option[]) => void;
  placeholder?: string;
  className?: string;
  maxDisplayItems?: number;
  required?: boolean;
  onSearch: (searchText: string) => void;
  isLoading?: boolean;
  hasMore?: boolean;
  loadMore?: () => void;
  error?: Error | string | null;
}

export function PaginatedSearchMultiSelectDropdown({
  label,
  options,
  selected,
  onChange,
  placeholder = "Select items",
  className,
  maxDisplayItems = 3,
  required = false,
  onSearch,
  isLoading = false,
  hasMore = false,
  loadMore,
  error,
}: PaginatedSearchMultiSelectProps) {
  const [open, setOpen] = React.useState(false);
  const [searchText, setSearchText] = React.useState("");

  const handleSelect = (option: Option) => {
    const isSelected = selected.some((item) => item.value === option.value);
    const newSelected = isSelected
      ? selected.filter((item) => item.value !== option.value)
      : [...selected, option];
    onChange(newSelected);
  };

  const selectedLabels = selected.map((option) => option.label);

  const handleSearch = (value: string) => {
    setSearchText(value);
    onSearch(value);
  };

  return (
    <div className="flex flex-col gap-1.25">
      {label && (
        <span className="text-xs">
          {label}
          <span className="ml-0.25">
            {required && <span className="text-destructive">*</span>}
          </span>
        </span>
      )}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger className="w-full">
          <Button
            variant="outline"
            tabIndex={-1}
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full hover:bg-transparent hover:text-current",
              className
            )}
          >
            <div
              title={selectedLabels.join(", ")}
              className="flex flex-wrap gap-1 items-center text-left text-xs min-w-0 w-full"
            >
              {selectedLabels.length === 0 ? (
                <span className="text-[var(--placeholder)] truncate w-full">
                  {placeholder}
                </span>
              ) : (
                <span className="truncate w-full">
                  {selectedLabels.slice(0, maxDisplayItems).join(", ")}
                  {selectedLabels.length > maxDisplayItems && (
                    <span className="pl-1">
                      +{selectedLabels.length - maxDisplayItems} more
                    </span>
                  )}
                </span>
              )}
            </div>
            <ChevronDown
              className={cn(
                "ml-2 h-5 w-5 shrink-0 transition-transform duration-200 text-[#696969]",
                open && "rotate-180"
              )}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-full p-0"
          style={{ width: "var(--radix-popover-trigger-width)" }}
          align="start"
        >
          <Command shouldFilter={false}>
            <CommandInput
              placeholder="Search"
              value={searchText}
              onValueChange={handleSearch}
              className=""
            />
            <CommandList>
              {options.length === 0 ? (
                <CommandEmpty>No options found.</CommandEmpty>
              ) : (
                <CommandGroup>
                  <div className="max-h-[12.5rem] overflow-y-auto">
                    <InfiniteScrollList
                      hasMore={hasMore}
                      loadMore={loadMore || (() => {})}
                      isLoading={isLoading}
                      error={error}
                    >
                      {options.map((option) => {
                        const isSelected = selected.some(
                          (item) => item.value === option.value
                        );
                        return (
                          <CommandItem
                            key={option.value}
                            onSelect={() => handleSelect(option)}
                            className="flex items-center gap-2 data-[selected=true]:bg-muted data-[selected=true]:text-muted-foreground"
                          >
                            <Checkbox
                              checked={isSelected}
                              onCheckedChange={() => handleSelect(option)}
                              className="mr-2 h-4 w-4"
                            />
                            <span>{option.label}</span>
                          </CommandItem>
                        );
                      })}
                      {isLoading && (
                        <div className="flex items-center justify-center py-2">
                          <span className="text-sm text-muted-foreground">
                            Loading...
                          </span>
                        </div>
                      )}
                    </InfiniteScrollList>
                  </div>
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
