import * as React from "react";
import { ChevronDown } from "lucide-react";
import { cn } from "../../lib/utils";
import { Button } from "../ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "../ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Checkbox } from "../ui/check-box";

export type Option = {
  label: string;
  value: string;
};

interface SearchSingleSelectDropdownProps {
  label?: string;
  options: Option[];
  selected: string;
  onChange: (selected: string) => void;
  placeholder?: string;
  className?: string;
  required?: boolean;
  helperText?: string;
}

export function SearchSingleSelectDropdown({
  label,
  options,
  selected,
  onChange,
  placeholder = "Select one",
  className,
  required = false,
  helperText,
}: SearchSingleSelectDropdownProps) {
  const [open, setOpen] = React.useState(false);

  const selectedLabels = options
    .filter((option) => selected.includes(option.value))
    .map((option) => option.label);

  return (
    <div className="flex flex-col gap-1.25">
      {label && (
        <span className="text-xs">
          {label}
          <span className="ml-0.25">
            {required && <span className="text-[#EB062B]">*</span>}
          </span>
        </span>
      )}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger className=" w-full">
          <Button
            variant="outline"
            tabIndex={-1}
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full hover:bg-transparent hover:text-current",
              helperText &&
                "border-[#EB062B] focus-visible:border-[#EB062B] selection:bg-[#EB062B] focus-visible:ring-[#EB062B]/20",
              className
            )}
          >
            <div
              title={selectedLabels.join(", ")}
              className="flex flex-wrap gap-1 items-center text-left text-xs min-w-0 w-full"
            >
              {selected.length === 0 ? (
                <span className="text-[var(--placeholder)] truncate w-full">
                  {placeholder}
                </span>
              ) : (
                <span className="truncate w-full">{selected}</span>
              )}
            </div>
            <ChevronDown
              className={cn(
                "ml-2 h-5 w-5 shrink-0 transition-transform duration-200 text-[#696969]",
                open && "rotate-180"
              )}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-full p-0"
          style={{ width: "var(--radix-popover-trigger-width)" }}
          align="start"
        >
          <Command>
            <CommandInput placeholder="Search" className="" />
            <CommandList>
              <CommandEmpty>No options found.</CommandEmpty>
              <CommandGroup>
                {options.map((option) => {
                  const isSelected = selected.includes(option.value);
                  return (
                    <CommandItem
                      key={option.value}
                      onSelect={() => {
                        onChange(option.value);
                        setOpen(false);
                      }}
                      className={cn(
                        "flex items-center gap-2 data-[selected=true]:bg-muted data-[selected=true]:text-muted-foreground ",
                        selected === option.value &&
                          "bg-muted text-muted-foreground"
                      )}
                    >
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={() => {
                          onChange(option.value);
                          setOpen(false);
                        }}
                        className="mr-2 h-4 w-4"
                      />
                      <span>{option.label}</span>
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      {helperText && (
        <span className="text-[10px] text-[#EB062B]">{helperText}</span>
      )}
    </div>
  );
}
