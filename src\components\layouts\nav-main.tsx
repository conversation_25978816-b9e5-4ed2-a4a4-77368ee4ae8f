"use client";

import { ChevronRight } from "lucide-react";
import { useRef, useState } from "react";
import { createPortal } from "react-dom";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "../ui/collapsible";
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "../design-system/sidebar";
import { Link } from "react-router-dom";

export interface NavMainItem {
  title: string;
  url: string;
  icon?: React.JSX.Element;
  isActive?: boolean;
  items?: {
    title: string;
    url: string;
  }[];
}

export function NavMain({ items }: { items: NavMainItem[] }) {
  const { visible } = useSidebar();
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const buttonRefs = useRef<Record<string, HTMLButtonElement | null>>({});
  const hoverTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const handleMouseLeave = () => {
    hoverTimeoutRef.current = setTimeout(() => {
      setHoveredItem(null);
    }, 300);
  };

  const handleMouseEnter = (title: string) => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }
    setHoveredItem(title);
  };

  return (
    <SidebarGroup>
      <SidebarMenu>
        {items.map((item) =>
          item.items?.length ? (
            visible ? (
              <Collapsible
                key={item.title}
                asChild
                defaultOpen={item.isActive}
                className="group/collapsible"
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton tooltip={item.title}>
                      {item.icon && item.icon}
                      <span>{item.title}</span>
                      {!!item.items?.length && (
                        <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                      )}
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.items?.map((subItem) => (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton
                            asChild
                            isActive={location.pathname === subItem.url}
                          >
                            <Link to={subItem.url}>
                              <span>{subItem.title}</span>
                            </Link>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            ) : (
              <div key={item.title} className="relative">
                <SidebarMenuButton
                  ref={(el) => {
                    if (el) {
                      buttonRefs.current[item.title] = el;
                    }
                  }}
                  onMouseEnter={() => handleMouseEnter(item.title)}
                  onMouseLeave={handleMouseLeave}
                  onFocus={() => handleMouseEnter(item.title)}
                  onBlur={handleMouseLeave}
                >
                  {item.icon && item.icon}
                  <span>{item.title}</span>
                  <ChevronRight className="ml-auto" />
                </SidebarMenuButton>
                {hoveredItem === item.title &&
                  buttonRefs.current[item.title] &&
                  createPortal(
                    <div
                      data-submenu={item.title}
                      className="fixed z-50 min-w-[150px] bg-black shadow-lg rounded-md"
                      style={{
                        top: buttonRefs.current[
                          item.title
                        ]!.getBoundingClientRect().top,
                        left: buttonRefs.current[
                          item.title
                        ]!.getBoundingClientRect().right,
                      }}
                      onMouseEnter={() => handleMouseEnter(item.title)}
                      onMouseLeave={handleMouseLeave}
                    >
                      <SidebarMenuSub>
                        {item.items.map((subItem) => (
                          <SidebarMenuSubItem key={subItem.title}>
                            <SidebarMenuSubButton
                              asChild
                              isActive={location.pathname === subItem.url}
                            >
                              <Link to={subItem.url} className="py-1 block">
                                {subItem.title}
                              </Link>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        ))}
                      </SidebarMenuSub>
                    </div>,
                    document.body
                  )}
              </div>
            )
          ) : (
            <Link key={item.title} to={item.url}>
              <SidebarMenuButton
                tooltip={item.title}
                isActive={location.pathname === item.url}
              >
                {item.icon && item.icon}
                <span>{item.title}</span>
              </SidebarMenuButton>
            </Link>
          )
        )}
      </SidebarMenu>
    </SidebarGroup>
  );
}
