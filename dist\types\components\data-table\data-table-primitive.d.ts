import { type ColumnDef, type OnChangeFn, type Row, type RowSelectionState, type SortingState, type VisibilityState } from "@tanstack/react-table";
import React from "react";
import type { SetState } from "../../types";
export type ExtendedColumnDef<TData, TValue> = ColumnDef<TData, TValue> & {
    show?: "mobile" | "desktop" | true | false;
    action?: boolean;
    primary?: boolean;
    secondary?: boolean;
    actions?: (row: Row<TData>) => {
        label: string;
        icon: React.JSX.Element;
        onClick: () => void;
    }[];
};
interface DataTableProps<TData, TValue> {
    columns: ExtendedColumnDef<TData, TValue>[];
    data: TData[];
    sorting?: SortingState;
    setSorting?: SetState<SortingState>;
    manualSorting?: boolean;
    onPaginationChange?: (skip: number, limit: number) => void;
    totalCount?: number;
    pageSize?: number;
    pageIndex?: number;
    showColumnToggle?: boolean;
    columnVisibility?: VisibilityState;
    onColumnVisibilityChange?: (value: VisibilityState) => void;
    rowSelection?: RowSelectionState;
    onRowSelectionChange?: (value: RowSelectionState) => void;
    enableRowSelection?: boolean;
    getRowId?: (originalRow: TData) => string;
    onRowClick?: (row: TData) => void;
    rowClassName?: (row: Row<TData>) => string;
    height?: string | number;
    isLoading?: boolean;
    onSortingChange?: OnChangeFn<SortingState>;
    disablePagination?: boolean;
    noDataMessage?: boolean;
    className?: string;
    maxHeight?: string;
    pageSizeOptions?: number[];
    defaultSorting?: SortingState;
    manualPagination: boolean;
    text: {
        noDataText: string;
        columns: string;
        pagination: {
            showText: string;
            totalCountText: string;
            loadingText: string;
            pageText: string;
            ofText: string;
            goToFirstPageText: string;
            goToPreviousPageText: string;
            goToNextPageText: string;
            goToLastPageText: string;
        };
        select: {
            selectAll: string;
            selectRow: string;
        };
    };
}
export declare function DataTablePrimitive<TData, TValue>({ columns, data, sorting: externalSorting, setSorting: externalSetSorting, manualSorting, onPaginationChange: handlePaginationChange, totalCount, pageSizeOptions, pageSize, pageIndex: externalPageIndex, showColumnToggle, columnVisibility: externalColumnVisibility, onColumnVisibilityChange, rowSelection: externalRowSelection, onRowSelectionChange, enableRowSelection, getRowId, onRowClick, rowClassName, isLoading: externalIsLoading, onSortingChange, disablePagination, noDataMessage, className, text, maxHeight, defaultSorting, manualPagination, }: DataTableProps<TData, TValue>): import("react/jsx-runtime").JSX.Element;
export {};
//# sourceMappingURL=data-table-primitive.d.ts.map