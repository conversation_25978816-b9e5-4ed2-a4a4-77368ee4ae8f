import { Checkbox } from "../ui/check-box";

interface CheckBoxFieldProps {
  id: string;
  label: string;
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export const CheckBoxField = ({
  id,
  label,
  checked,
  onCheckedChange,
  disabled = false,
  className = "",
}: CheckBoxFieldProps) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Checkbox
        id={id}
        checked={checked}
        onCheckedChange={onCheckedChange}
        disabled={disabled}
        className="h-4 w-4"
      />
      <label
        htmlFor={id}
        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
      >
        {label}
      </label>
    </div>
  );
};

interface CheckBoxGroupProps {
  className?: string;
  label?: string;
  options: string[];
  selectedOptions: string[];
  onCheckedChange: (option: string) => (checked: boolean) => void;
}

export const CheckBoxGroup = ({
  className = "",
  label,
  options,
  selectedOptions,
  onCheckedChange,
}: CheckBoxGroupProps) => {
  return (
    <div className="flex flex-col gap-1.25">
      {label && <label className="block text-xs">{label}</label>}
      <div className={`flex flex-row gap-5 h-9 ${className}`}>
        {options.map((option) => (
          <CheckBoxField
            key={option.toLowerCase()}
            id={
              label ? `${label}-${option.toLowerCase()}` : option.toLowerCase()
            }
            label={option}
            checked={selectedOptions?.includes(option)}
            onCheckedChange={onCheckedChange(option)}
          />
        ))}
      </div>
    </div>
  );
};
