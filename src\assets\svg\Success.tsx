import * as React from "react";

interface SuccessProps {
  className?: string;
  color?: string;
  size?: number;
}
const Success = ({
  className,
  color = "#15A48A",
  size = 16,
}: SuccessProps): React.JSX.Element => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8 0.5C3.8675 0.5 0.5 3.8675 0.5 8C0.5 12.1325 3.8675 15.5 8 15.5C12.1325 15.5 15.5 12.1325 15.5 8C15.5 3.8675 12.1325 0.5 8 0.5ZM11.585 6.275L7.3325 10.5275C7.2275 10.6325 7.085 10.6925 6.935 10.6925C6.785 10.6925 6.6425 10.6325 6.5375 10.5275L4.415 8.405C4.1975 8.1875 4.1975 7.8275 4.415 7.61C4.6325 7.3925 4.9925 7.3925 5.21 7.61L6.935 9.335L10.79 5.48C11.0075 5.2625 11.3675 5.2625 11.585 5.48C11.8025 5.6975 11.8025 6.05 11.585 6.275Z"
        fill={color}
      />
    </svg>
  );
};

export default Success;
