import { cva } from 'class-variance-authority';

export const buttonVariants = cva(
  "cursor-pointer inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-xs font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default: 'shadow-xs hover:bg-primary/90',
        contained: '',
        outline: 'border shadow-xs',
        ghost:
          'bg-white shadow-sm hover:text-accent-foreground dark:hover:bg-accent/50 cursor-pointer',
        link: 'underline underline-offset-4', // updated: no hover, always underlined
        base: 'focus:outline-none focus-visible:ring focus-visible:ring-offset-2 hover:bg-primary/5 disabled:opacity-50 disabled:pointer-events-none cursor-pointer',
      },
      size: {
        default: 'h-9 px-4 py-2 has-[>svg]:px-3',
        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',
        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',
        icon: 'size-9 !mx-0 !my-0',
      },
      color: {
        default:
          'text-foreground hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',
        muted: 'text-[#777777] border-[#777777] hover:bg-[#777777]/10',
        destructive: 'text-destructive border-destructive hover:bg-destructive/10',
        secondary: 'text-secondary-foreground bg-secondary shadow-xs hover:bg-secondary/80',
      },
    },
    compoundVariants: [
      {
        variant: 'contained',
        color: 'default',
        class: 'bg-primary text-primary-foreground hover:bg-primary/90 text-white',
      },
      {
        variant: 'contained',
        color: 'muted',
        class: 'bg-[#777777] text-white hover:bg-[#666666]',
      },
      {
        variant: 'contained',
        color: 'destructive',
        class: 'bg-destructive text-destructive-foreground text-white hover:bg-destructive/90',
      },
      {
        variant: 'contained',
        color: 'secondary',
        class: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
      },
    ],
    defaultVariants: {
      variant: 'default',
      size: 'default',
      color: 'default',
    },
  },
);
