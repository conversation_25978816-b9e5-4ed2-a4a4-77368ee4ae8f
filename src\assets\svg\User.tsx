import * as React from "react";

interface UserProps {
  className?: string;
  color?: string;
  size?: number;
}
const User = ({
  className,
  color = "white",
  size = 18,
}: UserProps): React.JSX.Element => (
  <svg
    className={className}
    width={size}
    height={size}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.87001 8.1525C6.79501 8.145 6.70501 8.145 6.62251 8.1525C4.83751 8.0925 3.42001 6.63 3.42001 4.83C3.42001 2.9925 4.90501 1.5 6.75001 1.5C8.58751 1.5 10.08 2.9925 10.08 4.83C10.0725 6.63 8.65501 8.0925 6.87001 8.1525Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.3075 3C13.7625 3 14.9325 4.1775 14.9325 5.625C14.9325 7.0425 13.8075 8.1975 12.405 8.25C12.345 8.2425 12.2775 8.2425 12.21 8.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.12001 10.92C1.30501 12.135 1.30501 14.115 3.12001 15.3225C5.18251 16.7025 8.56501 16.7025 10.6275 15.3225C12.4425 14.1075 12.4425 12.1275 10.6275 10.92C8.57251 9.5475 5.19001 9.5475 3.12001 10.92Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.755 15C14.295 14.8875 14.805 14.67 15.225 14.3475C16.395 13.47 16.395 12.0225 15.225 11.145C14.8125 10.83 14.31 10.62 13.7775 10.5"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default User;
