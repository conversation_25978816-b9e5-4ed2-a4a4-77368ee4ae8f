import React from "react";
import { But<PERSON> } from "../ui/button";
import Back from "../../assets/svg/back";
import SearchField from "./search-field";
import { useIsMobile } from "../../hooks/use-mobile";
interface NavbarProps {
  title?: string;
  enableBackButton?: boolean;
  primaryButton?: string | React.ReactNode;
  secondaryButton?: string | React.ReactNode;
  onBackButtonClick?: () => void;
  onPrimaryButtonClick?: () => void;
  onSecondaryButtonClick?: () => void;
  scrollbarExists?: boolean;
  searchBar?: boolean;
  primaryButtonLoading?: boolean;
  secondaryButtonLoading?: boolean;
  searchValue?: string;
  onSearchChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  secondaryButtonIcon?: React.ReactNode;
}

function Navbar({
  title,
  enableBackButton,
  primaryButton,
  secondaryButton,
  onBackButtonClick,
  onPrimaryButtonClick,
  onSecondaryButtonClick,
  scrollbarExists,
  searchBar,
  primaryButtonLoading,
  secondaryButtonLoading,
  searchValue,
  onSearchChange,
  secondaryButtonIcon,
}: NavbarProps) {
  const isMobile = useIsMobile();
  return (
    <>
      {!isMobile &&
        (title ||
          enableBackButton ||
          primaryButton ||
          secondaryButton ||
          searchBar) && (
          <div className="flex items-center justify-between px-6 py-4 bg-[#f1f5f9]">
            <div className="flex items-center gap-3">
              {enableBackButton && (
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={onBackButtonClick}
                  className="size-8 hover:bg-white/50 bg-transparent shadow-none [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-6"
                >
                  <Back />
                </Button>
              )}
              <span className="text-[1.25rem] font-medium">{title}</span>
              {searchBar && (
                <SearchField
                  placeholder="Search"
                  value={searchValue}
                  onChange={onSearchChange}
                />
              )}
            </div>
            <div
              className={`flex items-center gap-3 ${
                scrollbarExists ? "pr-3" : ""
              }`}
            >
              {primaryButton && (
                <Button
                  variant="default"
                  onClick={onPrimaryButtonClick}
                  className="text-black hover:bg-white/70 hover:text-black bg-white"
                  disabled={primaryButtonLoading}
                  isLoading={primaryButtonLoading}
                >
                  {primaryButton}
                </Button>
              )}
              {secondaryButton && (
                <Button
                  variant="contained"
                  color="destructive"
                  onClick={onSecondaryButtonClick}
                  disabled={secondaryButtonLoading}
                  isLoading={secondaryButtonLoading}
                >
                  {secondaryButton}
                </Button>
              )}
            </div>
          </div>
        )}
      {isMobile && (
        <div className="flex items-center px-5 py-3 justify-between bg-[#f1f5f9]">
          <div className="flex items-center gap-3">
            {enableBackButton && (
              <Button
                size="icon"
                variant="ghost"
                onClick={onBackButtonClick}
                className="hover:bg-white/50 bg-transparent shadow-none [&_svg:not([class*='size-'])]:size-5 size-7"
              >
                <Back size={40} />
              </Button>
            )}
            <span className="text-[1.25rem] font-medium">{title}</span>
            {searchBar && (
              <SearchField
                placeholder="Search"
                value={searchValue}
                onChange={onSearchChange}
              />
            )}
          </div>
          <div className="flex items-center gap-3">
            {primaryButton && (
              <Button
                variant="outline"
                onClick={onPrimaryButtonClick}
                className="py-1 px-3 h-7.5"
              >
                {primaryButton}
              </Button>
            )}
            {secondaryButton && (
              <Button
                variant="contained"
                color="destructive"
                onClick={onSecondaryButtonClick}
                className="py-1 px-3 h-7.5"
              >
                {secondaryButtonIcon && (
                  <span className="size-[0.875rem] flex items-center justify-center">
                    {secondaryButtonIcon}
                  </span>
                )}
                {secondaryButton}
              </Button>
            )}
          </div>
        </div>
      )}
    </>
  );
}

export default Navbar;
