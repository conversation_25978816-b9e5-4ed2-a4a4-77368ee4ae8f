import type { Column } from '@tanstack/react-table';
interface DataTableColumnHeaderProps<TData, TValue> extends React.HTMLAttributes<HTMLDivElement> {
    column: Column<TData, TValue>;
    textAlign?: 'left' | 'center' | 'right';
    headerTitle?: React.ReactNode;
}
export declare function DataTableColumnHeader<TData, TValue>({ column, title, textAlign, headerTitle, className, }: DataTableColumnHeaderProps<TData, TValue>): import("react/jsx-runtime").JSX.Element;
export {};
//# sourceMappingURL=data-table-column-header.d.ts.map