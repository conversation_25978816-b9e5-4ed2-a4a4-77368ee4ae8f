import type { Table } from "@tanstack/react-table";
interface DataTablePaginationProps<TData> {
    table: Table<TData>;
    pageSizeOptions: number[];
    onPaginationChange?: (skip: number, limit: number) => void;
    totalCount?: number;
    isLoading?: boolean;
    text: {
        showText: string;
        totalCountText: string;
        loadingText: string;
        pageText: string;
        ofText: string;
        goToFirstPageText: string;
        goToPreviousPageText: string;
        goToNextPageText: string;
        goToLastPageText: string;
    };
}
export declare function DataTablePagination<TData>({ table, onPaginationChange, totalCount, isLoading, text, pageSizeOptions, }: DataTablePaginationProps<TData>): import("react/jsx-runtime").JSX.Element;
export {};
//# sourceMappingURL=data-table-pagination.d.ts.map