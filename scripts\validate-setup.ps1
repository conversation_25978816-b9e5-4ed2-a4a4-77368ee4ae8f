# PowerShell script to validate the pipeline setup
# This script checks if all required files and configurations are in place

Write-Host "CPS Common UI - Setup Validation" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

$errors = @()
$warnings = @()

# Check required files
$requiredFiles = @(
    "package.json",
    "azure-pipelines.yml",
    "pnpm-lock.yaml",
    "rollup.config.js",
    "tsconfig.json"
)

Write-Host "`nChecking required files..." -ForegroundColor Yellow
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file" -ForegroundColor Green
    } else {
        Write-Host "✗ $file" -ForegroundColor Red
        $errors += "Missing required file: $file"
    }
}

# Check package.json configuration
Write-Host "`nValidating package.json..." -ForegroundColor Yellow
if (Test-Path "package.json") {
    try {
        $packageJson = Get-Content "package.json" | ConvertFrom-Json
        
        # Check required fields
        $requiredFields = @("name", "version", "main", "types", "scripts")
        foreach ($field in $requiredFields) {
            if ($packageJson.PSObject.Properties.Name -contains $field) {
                Write-Host "✓ package.json has '$field'" -ForegroundColor Green
            } else {
                Write-Host "✗ package.json missing '$field'" -ForegroundColor Red
                $errors += "package.json missing required field: $field"
            }
        }
        
        # Check build script
        if ($packageJson.scripts.PSObject.Properties.Name -contains "build") {
            Write-Host "✓ Build script found" -ForegroundColor Green
        } else {
            Write-Host "✗ Build script missing" -ForegroundColor Red
            $errors += "package.json missing build script"
        }
        
        # Check if package is scoped (recommended)
        if ($packageJson.name -like "@*/*") {
            Write-Host "✓ Package is scoped: $($packageJson.name)" -ForegroundColor Green
        } else {
            Write-Host "⚠ Package is not scoped: $($packageJson.name)" -ForegroundColor Yellow
            $warnings += "Consider scoping your package (e.g., @cps/common-ui) for better organization"
        }
        
    } catch {
        Write-Host "✗ Invalid package.json format" -ForegroundColor Red
        $errors += "package.json is not valid JSON"
    }
}

# Check build output structure
Write-Host "`nChecking build configuration..." -ForegroundColor Yellow
if (Test-Path "rollup.config.js") {
    Write-Host "✓ Rollup configuration found" -ForegroundColor Green
} else {
    Write-Host "⚠ Rollup configuration not found" -ForegroundColor Yellow
    $warnings += "Rollup configuration missing - build might not work as expected"
}

# Check TypeScript configuration
if (Test-Path "tsconfig.json") {
    Write-Host "✓ TypeScript configuration found" -ForegroundColor Green
} else {
    Write-Host "⚠ TypeScript configuration not found" -ForegroundColor Yellow
    $warnings += "TypeScript configuration missing - type definitions might not be generated"
}

# Check pipeline configuration
Write-Host "`nValidating pipeline configuration..." -ForegroundColor Yellow
if (Test-Path "azure-pipelines.yml") {
    $pipelineContent = Get-Content "azure-pipelines.yml" -Raw
    
    # Check for required sections
    $requiredSections = @("trigger:", "variables:", "stages:", "- stage: Build", "- stage: Publish")
    foreach ($section in $requiredSections) {
        if ($pipelineContent -match [regex]::Escape($section)) {
            Write-Host "✓ Pipeline has '$section'" -ForegroundColor Green
        } else {
            Write-Host "✗ Pipeline missing '$section'" -ForegroundColor Red
            $errors += "Pipeline missing required section: $section"
        }
    }
    
    # Check for feed configuration
    if ($pipelineContent -match "cps-ui-dev") {
        Write-Host "✓ Pipeline configured for cps-ui-dev feed" -ForegroundColor Green
    } else {
        Write-Host "⚠ Pipeline feed configuration unclear" -ForegroundColor Yellow
        $warnings += "Verify the artifacts feed name in azure-pipelines.yml"
    }
}

# Check Node.js and pnpm
Write-Host "`nChecking development environment..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✓ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "⚠ Node.js not found" -ForegroundColor Yellow
    $warnings += "Node.js not installed or not in PATH"
}

try {
    $pnpmVersion = pnpm --version
    Write-Host "✓ pnpm: $pnpmVersion" -ForegroundColor Green
} catch {
    Write-Host "⚠ pnpm not found" -ForegroundColor Yellow
    $warnings += "pnpm not installed - install with: npm install -g pnpm"
}

# Summary
Write-Host "`n" + "="*50 -ForegroundColor Blue
Write-Host "VALIDATION SUMMARY" -ForegroundColor Blue
Write-Host "="*50 -ForegroundColor Blue

if ($errors.Count -eq 0) {
    Write-Host "✓ No critical errors found!" -ForegroundColor Green
} else {
    Write-Host "✗ Found $($errors.Count) error(s):" -ForegroundColor Red
    foreach ($error in $errors) {
        Write-Host "  - $error" -ForegroundColor Red
    }
}

if ($warnings.Count -eq 0) {
    Write-Host "✓ No warnings!" -ForegroundColor Green
} else {
    Write-Host "⚠ Found $($warnings.Count) warning(s):" -ForegroundColor Yellow
    foreach ($warning in $warnings) {
        Write-Host "  - $warning" -ForegroundColor Yellow
    }
}

Write-Host "`nNext steps:" -ForegroundColor Blue
Write-Host "1. Fix any errors listed above" -ForegroundColor White
Write-Host "2. Review the PIPELINE_SETUP.md file for detailed setup instructions" -ForegroundColor White
Write-Host "3. Create the pipeline in Azure DevOps using azure-pipelines.yml" -ForegroundColor White
Write-Host "4. Test the setup by running: .\scripts\publish-local.ps1 -DryRun" -ForegroundColor White

if ($errors.Count -eq 0) {
    Write-Host "`n🎉 Setup looks good! Ready to create the pipeline." -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n❌ Please fix the errors before proceeding." -ForegroundColor Red
    exit 1
}
