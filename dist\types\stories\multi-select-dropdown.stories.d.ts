import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { MultiSelectDropdown } from "../components/design-system/multi-select-dropdown";
declare const meta: Meta<typeof MultiSelectDropdown>;
export default meta;
type Story = StoryObj<typeof MultiSelectDropdown>;
export declare const Default: Story;
export declare const WithSelections: Story;
export declare const Required: Story;
export declare const WithWarning: Story;
export declare const WithoutSelectAll: Story;
export declare const LimitedDisplay: Story;
//# sourceMappingURL=multi-select-dropdown.stories.d.ts.map