import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ConfirmProvider } from "../components/common/confirm-popup-provider";
import { useConfirm } from "../hooks/use-confirm";

const meta: Meta<typeof ConfirmProvider> = {
  title: "Design System/ConfirmationPopup",
  component: ConfirmProvider,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof ConfirmProvider>;

// Example component that uses the confirmation popup
const ConfirmationExample = () => {
  const { confirm } = useConfirm();

  const handleDelete = async () => {
    const confirmed = await confirm({
      title: "Delete Item",
      message:
        "Are you sure you want to delete this item? This action cannot be undone.",
      primaryButtonText: "Delete",
      secondaryButtonText: "Cancel",
    });

    if (confirmed) {
      console.log("Item deleted");
    } else {
      console.log("Delete cancelled");
    }
  };

  return (
    <div className="space-y-4">
      <button
        onClick={handleDelete}
        className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
      >
        Delete Item
      </button>
    </div>
  );
};

export const DeleteConfirmation: Story = {
  render: () => (
    <ConfirmProvider>
      <ConfirmationExample />
    </ConfirmProvider>
  ),
};
