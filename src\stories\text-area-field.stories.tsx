import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import Text<PERSON><PERSON><PERSON>ield from "../components/design-system/text-area-field";

const meta: Meta<typeof TextAreaField> = {
  title: "Design System/TextAreaField",
  component: TextAreaField,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: { control: "text" },
    placeholder: { control: "text" },
    value: { control: "text" },
    disabled: { control: "boolean" },
    required: { control: "boolean" },
    helperText: { control: "text" },
    onChange: { action: "changed" },
  },
};

export default meta;
type Story = StoryObj<typeof TextAreaField>;

export const Default: Story = {
  args: {
    label: "Text Area",
    placeholder: "Enter your text here...",
  },
};

export const WithValue: Story = {
  args: {
    label: "Text Area with Value",
    value: "This is some sample text that has been entered into the text area.",
  },
};

export const Required: Story = {
  args: {
    label: "Required Text Area",
    placeholder: "This field is required",
    required: true,
  },
};

export const WithWarning: Story = {
  args: {
    label: "Text Area with Warning",
    placeholder: "This field has a warning",
    helperText: "Required Field",
  },
};

export const Disabled: Story = {
  args: {
    label: "Disabled Text Area",
    placeholder: "This text area is disabled",
    disabled: true,
  },
};
