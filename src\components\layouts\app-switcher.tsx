import * as React from "react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "../design-system/sidebar";
import { useNavigate } from "react-router-dom";

export interface sidebarApps {
  name: string;
  logo: ({ color }: { color?: string }) => React.JSX.Element;
  url: string;
}

interface AppSwitcherProps {
  apps: sidebarApps[];
  activeApp: sidebarApps;
  setActiveApp: (app: sidebarApps) => void;
}

export function AppSwitcher({
  apps,
  activeApp,
  setActiveApp,
}: AppSwitcherProps) {
  const { isMobile, state } = useSidebar();
  const navigate = useNavigate();

  if (!activeApp) {
    return null;
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <div className="pb-6">
              {state === "collapsed" ? (
                <SidebarMenuButton
                  size="default"
                  variant="default"
                  className="rounded-lg bg-white hover:bg-white hover:text-black active:bg-white active:text-black flex items-center justify-center"
                >
                  <activeApp.logo />
                </SidebarMenuButton>
              ) : (
                <SidebarMenuButton
                  size="lg"
                  variant="light"
                  className="rounded-lg bg-white hover:bg-white active:bg-white text-black hover:text-black active:text-black"
                >
                  <activeApp.logo />
                  <div className="grid flex-1 text-left text-sm leading-tight data-[state=open]:hidden">
                    <span className="truncate font-medium">
                      {activeApp.name}
                    </span>
                  </div>
                  {/* <img src={chevronRight} alt="chevron-right" /> */}
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="12"
                    height="13"
                    viewBox="0 0 12 13"
                    fill="none"
                  >
                    <path
                      d="M4.4751 2.53999L7.7351 5.79999C8.1201 6.18499 8.1201 6.81499 7.7351 7.19999L4.4751 10.46"
                      stroke="black"
                      stroke-opacity="0.5"
                      stroke-width="1.5"
                      stroke-miterlimit="10"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </SidebarMenuButton>
              )}
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={10}
          >
            {apps.map((app) => (
              <DropdownMenuItem
                key={app.name}
                onClick={() => {
                  setActiveApp(app);
                  navigate(app.url);
                }}
                selected={activeApp.name === app.name}
                className="gap-2 p-2 data-[selected=true]:bg-black data-[selected=true]:text-white rounded-md"
              >
                <app.logo
                  color={activeApp.name === app.name ? "white" : "black"}
                />
                {app.name}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
