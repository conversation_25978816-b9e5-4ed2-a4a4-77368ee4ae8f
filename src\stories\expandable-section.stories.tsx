import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ExpandableSection } from "../components/design-system/expandable-section";
import { InputField } from "../components/design-system/input-field";

const meta: Meta<typeof ExpandableSection> = {
  title: "Design System/ExpandableSection",
  component: ExpandableSection,
  parameters: {
    // layout: 'centered',
  },
  tags: ["autodocs"],
  argTypes: {
    title: { control: "text" },
    defaultOpen: { control: "boolean" },
    open: { control: "boolean" },
    showWarning: { control: "boolean" },
    onOpenChange: { action: "opened/closed" },
  },
};

export default meta;
type Story = StoryObj<typeof ExpandableSection>;

export const Default: Story = {
  args: {
    title: "Section Title",
    children: (
      <div className="space-y-4">
        <p>This is the content of the expandable section.</p>
        <p>You can put any content here.</p>
      </div>
    ),
  },
};

export const DefaultOpen: Story = {
  args: {
    title: "Default Open Section",
    defaultOpen: true,
    children: (
      <div className="space-y-4">
        <p>This section is open by default.</p>
        <p>The content is visible when the page loads.</p>
      </div>
    ),
  },
};

export const WithWarning: Story = {
  args: {
    title: "Section with Warning",
    showWarning: true,
    children: (
      <div className="space-y-4">
        <p>This section has a warning icon.</p>
        <p>Use this when the section contains important information.</p>
      </div>
    ),
  },
};

export const WithFormContent: Story = {
  args: {
    title: "Form Section",
    children: (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 pb-3">
        <InputField label="Firstname" />
        <InputField label="Lastname" />
        <InputField label="Email" />
        <InputField label="Username" />
      </div>
    ),
  },
};
