import React from "react";

interface MagnifyingGlassProps {
  className?: string;
  color?: string;
  size?: number;
}

const MagnifyingGlass: React.FC<MagnifyingGlassProps> = ({
  className,
  color = "#EB062B",
  size = 16,
}) => (
  <svg
    className={className}
    width={size}
    height={size}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.428 12.4869L11.2659 10.3248C11.9212 9.46704 12.2805 8.4266 12.2807 7.32869C12.2807 6.00562 11.7654 4.76164 10.8297 3.82609C9.89412 2.89054 8.65031 2.37524 7.32707 2.37524C6.00399 2.37524 4.76001 2.89054 3.82446 3.82609C1.89315 5.75757 1.89315 8.90015 3.82446 10.8313C4.76001 11.767 6.00399 12.2823 7.32707 12.2823C8.42497 12.2822 9.46541 11.9228 10.3232 11.2675L12.4853 13.4296C12.6153 13.5598 12.7861 13.6249 12.9566 13.6249C13.1272 13.6249 13.2979 13.5598 13.428 13.4296C13.6884 13.1694 13.6884 12.7472 13.428 12.4869ZM4.76717 9.88859C3.35571 8.47713 3.35588 6.18042 4.76717 4.7688C5.45093 4.08521 6.36011 3.70858 7.32707 3.70858C8.29419 3.70858 9.20321 4.08521 9.88696 4.7688C10.5707 5.45256 10.9473 6.36174 10.9473 7.32869C10.9473 8.29582 10.5707 9.20483 9.88696 9.88859C9.20321 10.5723 8.29419 10.949 7.32707 10.949C6.36011 10.949 5.45093 10.5723 4.76717 9.88859Z"
      fill={color}
    />
  </svg>
);

export default MagnifyingGlass;
