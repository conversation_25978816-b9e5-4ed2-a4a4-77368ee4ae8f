import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import {
  RadioItem,
  RadioItemGroup,
} from "../components/design-system/radio-group";

const meta: Meta<typeof RadioItemGroup> = {
  title: "Design System/RadioGroup",
  component: RadioItemGroup,
  tags: ["autodocs"],
  argTypes: {
    defaultValue: {
      control: "text",
      description: "The default value of the radio group",
    },
    onValueChange: {
      action: "value changed",
      description: "Callback when the value changes",
    },
  },
};

export default meta;
type Story = StoryObj<typeof RadioItemGroup>;

export const Default: Story = {
  args: {
    defaultValue: "option1",
  },
  render: (args: Story["args"]) => (
    <RadioItemGroup {...args}>
      <RadioItem value="option1">Option 1</RadioItem>
      <RadioItem value="option2">Option 2</RadioItem>
      <RadioItem value="option3">Option 3</RadioItem>
    </RadioItemGroup>
  ),
};

export const WithCustomStyling: Story = {
  args: {
    defaultValue: "option1",
  },
  render: (args: Story["args"]) => (
    <RadioItemGroup {...args}>
      <RadioItem value="option1" className="border-primary">
        Custom Styled Option 1
      </RadioItem>
      <RadioItem value="option2" className="border-primary">
        Custom Styled Option 2
      </RadioItem>
    </RadioItemGroup>
  ),
};

export const Disabled: Story = {
  args: {
    defaultValue: "option1",
  },
  render: (args: Story["args"]) => (
    <RadioItemGroup {...args}>
      <RadioItem value="option1" disabled>
        Disabled Option 1
      </RadioItem>
      <RadioItem value="option2" disabled>
        Disabled Option 2
      </RadioItem>
    </RadioItemGroup>
  ),
};
