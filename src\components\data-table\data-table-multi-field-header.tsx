import type { OnChangeFn, SortingState } from "@tanstack/react-table";
import { ArrowDown, ArrowUp, ChevronsUpDown } from "lucide-react";
import { cn } from "../../lib/utils";
import { Popover, PopoverTrigger, PopoverContent } from "../ui/popover";

type fileldType = {
  sortBy: string;
  sortOrder: "asc" | "desc";
  label: string;
  type?: "number" | "string";
};
interface DataTableColumnHeaderProps {
  title?: string;
  className?: string;
  headerTitle?: React.ReactNode;
  fields: fileldType[];
  onSelection: OnChangeFn<SortingState>;
  sorting: SortingState;
}

export function MultiFieldHeader({
  title,
  headerTitle,
  className,
  fields,
  onSelection,
  sorting,
}: DataTableColumnHeaderProps) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className={cn("flex items-center space-x-2", className)}>
          <div className="h-8 data-[state=open]:bg-accent flex items-center cursor-pointer text-[#67748E]">
            <span>{title || headerTitle}</span>
            {sorting.length > 0 &&
            fields.some((fl) => fl.sortBy === sorting[0].id) ? (
              <>
                {sorting[0].desc ? (
                  <ArrowDown className="ml-2 h-4 w-4" />
                ) : (
                  <ArrowUp className="ml-2 h-4 w-4" />
                )}
              </>
            ) : (
              <ChevronsUpDown className="ml-2 h-4 w-4" />
            )}
          </div>
        </div>
      </PopoverTrigger>

      <PopoverContent
        className="p-4 bg-white shadow-sm rounded-md"
        align="start"
      >
        <div className="flex flex-col gap-1 space-y-1">
          {fields.map((field, idx) => (
            <p
              key={idx}
              className="justify-start pb-1 not-last:border-b cursor-pointer"
              onClick={() =>
                onSelection([
                  { id: field.sortBy, desc: field.sortOrder === "desc" },
                ])
              } // ascending
            >
              {field.label} :{" "}
              {field.type === "number"
                ? field.sortOrder
                : field.sortOrder === "asc"
                ? "A-Z"
                : "Z-A"}
            </p>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}
