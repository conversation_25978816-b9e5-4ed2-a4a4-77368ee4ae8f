import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import DrawerModal from "../components/design-system/drawer-modal";
import { Button } from "../components/ui/button";

const meta: Meta<typeof DrawerModal> = {
  title: "Design System/DrawerModal",
  component: DrawerModal,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    title: { control: "text" },
  },
};

export default meta;
type Story = StoryObj<typeof DrawerModal>;

export const Default: Story = {
  args: {
    title: "Drawer Title",
    trigger: <Button>Open Drawer</Button>,
    children: (
      <div className="space-y-4">
        <p>This is the content of the drawer.</p>
        <p>You can put any content here.</p>
      </div>
    ),
  },
};

export const WithFooter: Story = {
  args: {
    title: "Drawer with Footer",
    trigger: <Button>Open Drawer with Footer</Button>,
    children: (
      <div className="space-y-4">
        <p>This drawer has a footer section.</p>
        <p>The footer can contain action buttons or other content.</p>
      </div>
    ),
    footer: (
      <div className="flex justify-end gap-2">
        <Button variant="outline">Cancel</Button>
        <Button>Save Changes</Button>
      </div>
    ),
  },
};

export const WithFormContent: Story = {
  args: {
    title: "Form Drawer",
    trigger: <Button>Open Form Drawer</Button>,
    children: (
      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium">Name</label>
          <input type="text" className="w-full border rounded p-2 mt-1" />
        </div>
        <div>
          <label className="text-sm font-medium">Email</label>
          <input type="email" className="w-full border rounded p-2 mt-1" />
        </div>
      </div>
    ),
    footer: (
      <div className="flex justify-end gap-2">
        <Button variant="outline">Cancel</Button>
        <Button>Submit</Button>
      </div>
    ),
  },
};
