import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { SearchSingleSelectDropdown } from "../components/design-system/search-single-select-dropdown";

const meta: Meta<typeof SearchSingleSelectDropdown> = {
  title: "Design System/SearchSingleSelectDropdown",
  component: SearchSingleSelectDropdown,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: { control: "text" },
    placeholder: { control: "text" },
    selected: { control: "text" },
    required: { control: "boolean" },
    helperText: { control: "text" },
    onChange: { action: "changed" },
  },
};

export default meta;
type Story = StoryObj<typeof SearchSingleSelectDropdown>;

const options = [
  { label: "Option 1", value: "option1" },
  { label: "Option 2", value: "option2" },
  { label: "Option 3", value: "option3" },
  { label: "Option 4", value: "option4" },
  { label: "Option 5", value: "option5" },
];

export const Default: Story = {
  args: {
    label: "Select Option",
    options,
    selected: "",
    placeholder: "Select an option",
  },
};

export const WithSelection: Story = {
  args: {
    label: "Select Option",
    options,
    selected: "option2",
    placeholder: "Select an option",
  },
};

export const Required: Story = {
  args: {
    label: "Select Option",
    options,
    selected: "",
    required: true,
  },
};

export const WithWarning: Story = {
  args: {
    label: "Select Option",
    options,
    selected: "",
    helperText: "Required Field",
  },
};

export const WithLongOptions: Story = {
  args: {
    label: "Select Option",
    options: [
      ...options,
      {
        label: "This is a very long option that might need truncation",
        value: "long1",
      },
      {
        label:
          "Another long option with lots of text to demonstrate truncation",
        value: "long2",
      },
    ],
    selected: "",
  },
};

export const WithCustomClassName: Story = {
  args: {
    label: "Select Option",
    options,
    selected: "",
    className: "bg-gray-50",
  },
};
