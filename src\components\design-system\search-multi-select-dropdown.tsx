import * as React from "react";
import { ChevronDown } from "lucide-react";
import { cn } from "../../lib/utils";
import { Button } from "../ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "../ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Checkbox } from "../ui/check-box";

export type Option = {
  label: string;
  value: string;
};

interface SearchMultiSelectProps {
  label?: string;
  options: Option[];
  selected: string[];
  onChange: (selected: string[]) => void;
  placeholder?: string;
  className?: string;
  maxDisplayItems?: number;
  required?: boolean;
}

export function SearchMultiSelectDropdown({
  label,
  options,
  selected,
  onChange,
  placeholder = "Select items",
  className,
  maxDisplayItems = 3,
  required = false,
}: SearchMultiSelectProps) {
  const [open, setOpen] = React.useState(false);

  const handleSelect = (value: string) => {
    const newSelected = selected.includes(value)
      ? selected.filter((item) => item !== value)
      : [...selected, value];
    onChange(newSelected);
  };

  const handleSelectAll = () => {
    if (selected.length === options.length) {
      onChange([]);
    } else {
      onChange(options.map((option) => option.value));
    }
  };

  const selectedLabels = options
    .filter((option) => selected.includes(option.value))
    .map((option) => option.label);

  const isAllSelected = selected.length === options.length;

  return (
    <div className="flex flex-col gap-1.25">
      {label && (
        <span className="text-xs">
          {label}
          <span className="ml-0.25">
            {required && <span className="text-[#EB062B]">*</span>}
          </span>
        </span>
      )}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger className=" w-full">
          <Button
            variant="outline"
            tabIndex={-1}
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full hover:bg-transparent hover:text-current",
              className
            )}
          >
            <div
              title={selectedLabels.join(", ")}
              className="flex flex-wrap gap-1 items-center text-left text-xs min-w-0 w-full"
            >
              {selectedLabels.length === 0 ? (
                <span className="text-[var(--placeholder)] truncate w-full">
                  {placeholder}
                </span>
              ) : (
                <span className="truncate w-full">
                  {selectedLabels.slice(0, maxDisplayItems).join(", ")}
                  {selectedLabels.length > maxDisplayItems && (
                    <span className="pl-1">
                      +{selectedLabels.length - maxDisplayItems} more
                    </span>
                  )}
                </span>
              )}
            </div>
            <ChevronDown
              className={cn(
                "ml-2 h-5 w-5 shrink-0 transition-transform duration-200 text-[#696969]",
                open && "rotate-180"
              )}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-full p-0"
          style={{ width: "var(--radix-popover-trigger-width)" }}
          align="start"
        >
          <Command>
            <CommandInput placeholder="Search" className="" />
            <CommandList>
              <CommandEmpty>No options found.</CommandEmpty>
              <CommandGroup>
                <CommandItem
                  onSelect={handleSelectAll}
                  className="flex items-center gap-2 font-medium data-[selected=true]:bg-muted data-[selected=true]:text-muted-foreground"
                >
                  <Checkbox
                    checked={isAllSelected}
                    onCheckedChange={handleSelectAll}
                    className="mr-2 h-4 w-4"
                  />
                  Select All
                </CommandItem>

                {/* Individual Options */}
                {options.map((option) => {
                  const isSelected = selected.includes(option.value);
                  return (
                    <CommandItem
                      key={option.value}
                      onSelect={() => handleSelect(option.value)}
                      className="flex items-center gap-2 font-medium data-[selected=true]:bg-muted data-[selected=true]:text-muted-foreground"
                    >
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={() => handleSelect(option.value)}
                        className="mr-2 h-4 w-4"
                      />
                      <span>{option.label}</span>
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
