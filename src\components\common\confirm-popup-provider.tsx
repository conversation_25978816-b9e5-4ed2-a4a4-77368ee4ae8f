import React, {
  createContext,
  useState,
  type ReactNode,
  useCallback,
  useRef,
} from "react";
import ConfirmationPopup from "../design-system/confirmation-popup";

interface ConfirmOptions {
  title: string;
  message: string;
  primaryButtonText: string;
  secondaryButtonText: string;
  onPrimaryButtonClick?: () => void;
  onSecondaryButtonClick?: () => void;
}

export interface ConfirmContextValue {
  showConfirm: (options: ConfirmOptions) => void;
  hideConfirm: () => void;
  confirmOptions: ConfirmOptions | null;
  confirm: (
    options: Omit<
      ConfirmOptions,
      "onPrimaryButtonClick" | "onSecondaryButtonClick"
    >
  ) => Promise<boolean>;
}

export const ConfirmContext = createContext<ConfirmContextValue | undefined>(
  undefined
);

export const ConfirmProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [confirmOptions, setConfirmOptions] = useState<ConfirmOptions | null>(
    null
  );
  const promiseRef = useRef<{
    resolve: ((value: boolean) => void) | null;
    reject: ((reason?: unknown) => void) | null;
  }>({ resolve: null, reject: null });

  const showConfirm = useCallback((options: ConfirmOptions) => {
    setConfirmOptions(options);
  }, []);

  const hideConfirm = useCallback(() => {
    setConfirmOptions(null);
    promiseRef.current = { resolve: null, reject: null };
  }, []);

  const confirm: ConfirmContextValue["confirm"] = useCallback(
    (options) => {
      return new Promise<boolean>((resolve, reject) => {
        promiseRef.current = { resolve, reject };
        setConfirmOptions({
          ...options,
          onPrimaryButtonClick: () => {
            promiseRef.current.resolve?.(true);
            hideConfirm();
          },
          onSecondaryButtonClick: () => {
            promiseRef.current.resolve?.(false);
            hideConfirm();
          },
        });
      });
    },
    [hideConfirm]
  );

  const value = React.useMemo(
    () => ({
      showConfirm,
      hideConfirm,
      confirmOptions,
      confirm,
    }),
    [showConfirm, hideConfirm, confirmOptions, confirm]
  );

  return (
    <ConfirmContext.Provider value={value}>
      {children}
      <ConfirmationPopup />
    </ConfirmContext.Provider>
  );
};
