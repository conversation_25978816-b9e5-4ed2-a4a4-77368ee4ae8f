import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import DrawerModal from "../components/design-system/drawer-modal";
declare const meta: Meta<typeof DrawerModal>;
export default meta;
type Story = StoryObj<typeof DrawerModal>;
export declare const Default: Story;
export declare const WithFooter: Story;
export declare const WithFormContent: Story;
//# sourceMappingURL=drawer-modal.stories.d.ts.map