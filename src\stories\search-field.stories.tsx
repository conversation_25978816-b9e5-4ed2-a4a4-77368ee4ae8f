import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import <PERSON>Field from "../components/design-system/search-field";

const meta: Meta<typeof SearchField> = {
  title: "Design System/SearchField",
  component: SearchField,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: { control: "text" },
    placeholder: { control: "text" },
    value: { control: "text" },
    disabled: { control: "boolean" },
    required: { control: "boolean" },
    autoFocus: { control: "boolean" },
    onChange: { action: "changed" },
    onKeyDown: { action: "keydown" },
    onFocus: { action: "focused" },
  },
};

export default meta;
type Story = StoryObj<typeof SearchField>;

export const Default: Story = {
  args: {
    label: "Search",
    placeholder: "Search...",
  },
};

export const WithValue: Story = {
  args: {
    label: "Search",
    value: "Search term",
  },
};

export const Required: Story = {
  args: {
    label: "Required Search",
    placeholder: "This field is required",
    required: true,
  },
};

export const Disabled: Story = {
  args: {
    label: "Disabled Search",
    placeholder: "This search is disabled",
    disabled: true,
  },
};

export const AutoFocus: Story = {
  args: {
    label: "Auto Focus Search",
    placeholder: "This field will auto focus",
    autoFocus: true,
  },
};
