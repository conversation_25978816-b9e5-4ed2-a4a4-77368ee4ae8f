import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { CheckBoxField } from "../components/design-system/check-box";

const meta: Meta<typeof CheckBoxField> = {
  title: "Design System/CheckBox",
  component: CheckBoxField,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    id: { control: "text" },
    label: { control: "text" },
    checked: { control: "boolean" },
    disabled: { control: "boolean" },
    onCheckedChange: { action: "checked" },
  },
};

export default meta;
type Story = StoryObj<typeof CheckBoxField>;

export const Default: Story = {
  args: {
    id: "checkbox-1",
    label: "Checkbox Label",
  },
};

export const Checked: Story = {
  args: {
    id: "checkbox-2",
    label: "Checked Checkbox",
    checked: true,
  },
};

export const Disabled: Story = {
  args: {
    id: "checkbox-3",
    label: "Disabled Checkbox",
    disabled: true,
  },
};
export const Disabled11: Story = {
  args: {
    id: "checkbox-4",
    label: "Custom checbox",
  },
  render: (arg) => (
    <CheckBoxField {...arg} className="flex-col-reverse items-start" />
  ),
};
