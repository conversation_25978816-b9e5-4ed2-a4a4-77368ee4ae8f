import * as React from "react";

interface CheckProps {
  className?: string;
  color?: string;
  size?: number;
}
const Check = ({
  className,
  color = "#30C00C",
  size = 16,
}: CheckProps): React.JSX.Element => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0ZM11.0088 6.18262C10.765 5.93897 10.3697 5.93896 10.126 6.18262L7.125 9.18359L6.06543 8.125C5.82169 7.88134 5.42636 7.88135 5.18262 8.125C4.93899 8.36878 4.93896 8.76411 5.18262 9.00781L6.42578 10.249C6.61053 10.4355 6.86246 10.5402 7.125 10.54C7.38701 10.5408 7.63832 10.4366 7.82324 10.251L11.0088 7.06543C11.2525 6.82159 11.2526 6.4263 11.0088 6.18262Z"
        fill={color}
      />
    </svg>
  );
};

export default Check;
