import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  type OnChangeFn,
  type Row,
  type RowSelectionState,
  type SortingState,
  useReactTable,
  type VisibilityState,
} from "@tanstack/react-table";
import React, { useEffect, useState } from "react";
import { Button } from "../ui/button";
import { Checkbox } from "../ui/check-box";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { cn } from "../../lib/utils";
import { ChevronDown } from "lucide-react";
import { DataTablePagination } from "./data-table-pagination";
import {
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
  Table,
} from "../ui/table";
import NoDataFound from "../common/no-data-found-primitive";
import LoadingSpinner from "../ui/loading-spinner";
import { useIsMobile } from "../../hooks/use-mobile";
import { Separator } from "../ui/separator";
import More from "../../assets/svg/More";
import { Card } from "../ui/card";
import DrawerModal from "../design-system/drawer-modal";
import InfiniteScrollList from "../design-system/infinite-scroll-list";
import type { SetState } from "../../types";

export type ExtendedColumnDef<TData, TValue> = ColumnDef<TData, TValue> & {
  show?: "mobile" | "desktop" | true | false;
  action?: boolean;
  primary?: boolean;
  secondary?: boolean;
  actions?: (row: Row<TData>) => {
    label: string;
    icon: React.JSX.Element;
    onClick: () => void;
  }[];
};

interface DataTableProps<TData, TValue> {
  columns: ExtendedColumnDef<TData, TValue>[];
  data: TData[];
  sorting?: SortingState;
  setSorting?: SetState<SortingState>;
  manualSorting?: boolean;
  onPaginationChange?: (skip: number, limit: number) => void;
  totalCount?: number;
  pageSize?: number;
  pageIndex?: number;
  showColumnToggle?: boolean;
  columnVisibility?: VisibilityState;
  onColumnVisibilityChange?: (value: VisibilityState) => void;
  rowSelection?: RowSelectionState;
  onRowSelectionChange?: (value: RowSelectionState) => void;
  enableRowSelection?: boolean;
  getRowId?: (originalRow: TData) => string;
  onRowClick?: (row: TData) => void;
  rowClassName?: (row: Row<TData>) => string;
  height?: string | number;
  isLoading?: boolean;
  onSortingChange?: OnChangeFn<SortingState>;
  disablePagination?: boolean;
  noDataMessage?: boolean;
  className?: string;
  maxHeight?: string;
  pageSizeOptions?: number[];
  defaultSorting?: SortingState;
  manualPagination: boolean;
  text: {
    noDataText: string;
    columns: string;
    pagination: {
      showText: string;
      totalCountText: string;
      loadingText: string;
      pageText: string;
      ofText: string;
      goToFirstPageText: string;
      goToPreviousPageText: string;
      goToNextPageText: string;
      goToLastPageText: string;
    };
    select: {
      selectAll: string;
      selectRow: string;
    };
  };
}

export function DataTablePrimitive<TData, TValue>({
  columns,
  data,
  sorting: externalSorting,
  setSorting: externalSetSorting,
  manualSorting,
  onPaginationChange: handlePaginationChange,
  totalCount = data.length,
  pageSizeOptions = [15, 30, 60],
  pageSize = pageSizeOptions[0],
  pageIndex: externalPageIndex,
  showColumnToggle = true,
  columnVisibility: externalColumnVisibility,
  onColumnVisibilityChange,
  rowSelection: externalRowSelection,
  onRowSelectionChange,
  enableRowSelection = false,
  getRowId,
  onRowClick,
  rowClassName,
  isLoading: externalIsLoading,
  onSortingChange,
  disablePagination = false,
  noDataMessage,
  className,
  text,
  maxHeight,
  defaultSorting,
  manualPagination,
}: DataTableProps<TData, TValue>) {
  const isMobile = useIsMobile();
  const [internalSorting, setInternalSorting] = useState<SortingState>(
    defaultSorting ?? []
  );
  const [pageIndex, setPageIndex] = useState(externalPageIndex ?? 0);
  const [internalColumnVisibility, setInternalColumnVisibility] =
    useState<VisibilityState>(() => {
      // Initialize column visibility based on show property
      const initialVisibility: VisibilityState = {};
      columns.forEach((col) => {
        if (col.id) {
          const extendedCol = col as ExtendedColumnDef<TData, TValue>;
          initialVisibility[col.id] = extendedCol.show !== false;
        }
      });
      return initialVisibility;
    });
  const [internalRowSelection, setInternalRowSelection] =
    useState<RowSelectionState>({});
  const [isLoading, setIsLoading] = useState(false);
  const [prevPageIndex, setPrevPageIndex] = useState(pageIndex);
  const [prevPageSize, setPrevPageSize] = useState(pageSize);

  const onPaginationChange = (skip: number, limit: number) => {
    const newPageIndex = Math.floor(skip / limit);
    setPageIndex(newPageIndex);
    handlePaginationChange?.(skip, limit);
  };

  // Track pagination changes to show loading state
  useEffect(() => {
    if (prevPageIndex !== pageIndex || prevPageSize !== pageSize) {
      // setIsLoading(true);

      // Reset loading state after a short delay to simulate loading
      const timeout = setTimeout(() => {
        setIsLoading(false);
      }, 500);

      setPrevPageIndex(pageIndex);
      setPrevPageSize(pageSize);

      return () => clearTimeout(timeout);
    }
  }, [pageIndex, pageSize, prevPageIndex, prevPageSize]);

  // Use either external or internal loading state
  const isTableLoading =
    externalIsLoading !== undefined ? externalIsLoading : isLoading;

  // Use either external or internal state for column visibility and row selection
  const columnVisibilityState =
    externalColumnVisibility ?? internalColumnVisibility;
  const rowSelectionState = externalRowSelection ?? internalRowSelection;

  const handleColumnVisibilityChange: OnChangeFn<VisibilityState> = (
    updatedVisibility
  ) => {
    if (onColumnVisibilityChange) {
      // If it's a function updater, call it with current state to get the new value
      const newState =
        typeof updatedVisibility === "function"
          ? updatedVisibility(columnVisibilityState)
          : updatedVisibility;

      onColumnVisibilityChange(newState);
    } else {
      setInternalColumnVisibility(updatedVisibility);
    }
  };

  const handleRowSelectionChange: OnChangeFn<RowSelectionState> = (
    updatedSelection
  ) => {
    if (onRowSelectionChange) {
      // If it's a function updater, call it with current state to get the new value
      const newState =
        typeof updatedSelection === "function"
          ? updatedSelection(rowSelectionState)
          : updatedSelection;

      onRowSelectionChange(newState);
    } else {
      setInternalRowSelection(updatedSelection);
    }
  };

  // Add selection column if row selection is enabled
  const selectionColumns: ColumnDef<TData, TValue>[] = enableRowSelection
    ? [
        {
          id: "select",
          header: ({ table }) => (
            <div className="px-2 hidden md:block">
              <Checkbox
                checked={table.getIsAllPageRowsSelected()}
                onCheckedChange={(value) =>
                  table.toggleAllPageRowsSelected(!!value)
                }
                aria-label={text?.select?.selectAll}
              />
            </div>
          ),
          cell: ({ row }) => (
            <div className="px-2">
              <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label={text?.select?.selectRow}
              />
            </div>
          ),
          enableSorting: false,
          enableHiding: false,
        } as ColumnDef<TData, TValue>,
        ...columns,
      ]
    : columns;

  const table = useReactTable({
    data,
    columns: selectionColumns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange:
      onSortingChange ?? externalSetSorting ?? setInternalSorting,
    getSortedRowModel: !manualSorting ? getSortedRowModel() : undefined,
    manualSorting: manualSorting,
    enableSortingRemoval: true,
    onColumnVisibilityChange: handleColumnVisibilityChange,
    onRowSelectionChange: handleRowSelectionChange,
    state: {
      sorting: externalSorting ?? internalSorting,
      columnVisibility: {
        ...columnVisibilityState,
        ...Object.fromEntries(
          columns
            .filter(
              (col): col is ExtendedColumnDef<TData, TValue> =>
                col.id !== undefined
            )
            .map((col) => [col.id, col.show !== false])
        ),
      },
      rowSelection: rowSelectionState,
      pagination: {
        pageIndex,
        pageSize,
      },
    },
    enableRowSelection,
    manualPagination: manualPagination,
    pageCount: totalCount ? Math.ceil(totalCount / pageSize) : undefined,
    getRowId: getRowId,
    enableMultiSort: false,
    isMultiSortEvent: () => false,
    maxMultiSortColCount: 1,
    initialState: {
      columnSizing: {
        select: 40, // force "select" column to start at 40px
      },
    },
  });

  // Format a column ID into a readable label
  const formatColumnId = (id: string): string => {
    return id
      .replace(/_/g, " ")
      .replace(/([a-z])([A-Z])/g, "$1 $2") // Convert camelCase to spaces
      .replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize first letter of each word
  };

  // Handle row click event
  const handleRowClick = (
    e: React.MouseEvent<HTMLTableRowElement>,
    row: Row<TData>
  ) => {
    if (onRowClick) {
      // Only handle clicks on the row itself, not on interactive elements
      const target = e.target as HTMLElement;
      const closestLink = target.closest("a");
      const closestButton = target.closest("button");
      const closestCheckbox = target.closest('input[type="checkbox"]');

      if (!closestLink && !closestButton && !closestCheckbox) {
        onRowClick(row.original);
      }
    }
  };

  // If there's no data and we're in loading state, show a centered spinner
  if (isTableLoading && data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  let adjustedMaxHeight;

  if (typeof maxHeight === "string") {
    // Remove outer calc() if present
    const inner = maxHeight.replace(/^calc\((.*)\)$/, "$1");
    adjustedMaxHeight = `calc(${inner} - 50px)`;
  } else if (typeof maxHeight === "number") {
    adjustedMaxHeight = maxHeight - 50;
  } else {
    adjustedMaxHeight = undefined; // don't apply height at all
  }

  return (
    <>
      {isMobile && (
        <div className="flex flex-col gap-2">
          {isTableLoading && data.length === 0 && (
            <div className="flex items-center justify-center h-24">
              <LoadingSpinner />
            </div>
          )}
          {table.getRowModel().rows.length ? (
            <InfiniteScrollList
              hasMore={table.getRowModel().rows.length < (totalCount || 0)}
              loadMore={() => {
                onPaginationChange?.((pageIndex + 1) * pageSize, pageSize);
              }}
              isLoading={isTableLoading}
            >
              {table.getRowModel().rows.map((row) => (
                <div key={row.id} className={row.id}>
                  <Card key={row.id} className="p-4 gap-2">
                    <div key={row.id} className="flex justify-between">
                      <div className="flex flex-col">
                        {row
                          .getVisibleCells()
                          .filter((cell) => {
                            const column = cell.column
                              .columnDef as ExtendedColumnDef<TData, TValue>;
                            return column.primary;
                          })
                          .map((cell) => (
                            <div key={cell.id}>
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext()
                              )}
                            </div>
                          ))}
                        {row
                          .getVisibleCells()
                          .filter((cell) => {
                            const column = cell.column
                              .columnDef as ExtendedColumnDef<TData, TValue>;
                            return column.secondary;
                          })
                          .map((cell) => (
                            <div key={cell.id}>
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext()
                              )}
                            </div>
                          ))}
                      </div>
                      {row
                        .getVisibleCells()
                        .filter((cell) => {
                          const column = cell.column
                            .columnDef as ExtendedColumnDef<TData, TValue>;
                          return column.action && column.actions;
                        })
                        .map((cell) => {
                          const column = cell.column
                            .columnDef as ExtendedColumnDef<TData, TValue>;
                          return (
                            column.actions?.(row) &&
                            column.actions(row).length > 0 && (
                              <DrawerModal
                                trigger={
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="shadow-none w-5 h-5"
                                  >
                                    <More />
                                  </Button>
                                }
                              >
                                <div className="flex flex-col gap-1 items-start ">
                                  <div className="flex flex-col gap-1 items-start ">
                                    {column.actions(row).map((action) => (
                                      <Button
                                        key={action.label}
                                        variant="ghost"
                                        className="shadow-none hover:text-black p-0"
                                        onClick={action.onClick}
                                      >
                                        <span className="flex items-center gap-2">
                                          {action.icon}
                                          {action.label}
                                        </span>
                                      </Button>
                                    ))}
                                  </div>
                                </div>
                              </DrawerModal>
                            )
                          );
                        })}
                    </div>
                    {row.getVisibleCells().filter((cell) => {
                      const column = cell.column.columnDef as ExtendedColumnDef<
                        TData,
                        TValue
                      >;
                      return (
                        column.actions ||
                        column.action ||
                        column.primary ||
                        column.secondary
                      );
                    }).length > 1 && <Separator />}
                    <div className="flex flex-col gap-3">
                      {row
                        .getVisibleCells()
                        .filter((cell) => {
                          const column = cell.column
                            .columnDef as ExtendedColumnDef<TData, TValue>;
                          return (
                            !column.primary &&
                            !column.secondary &&
                            !column.action &&
                            column.show !== "desktop" &&
                            column.show !== false
                          );
                        })
                        .map((cell) => (
                          <div key={cell.id} className="flex justify-between">
                            <span className="text-sm text-[#031E2E]">
                              {(() => {
                                const header = table
                                  .getHeaderGroups()[0]
                                  .headers.find(
                                    (header) =>
                                      header.column.id === cell.column.id
                                  );
                                if (!header) return "";
                                return flexRender(
                                  header.column.columnDef.header,
                                  header.getContext()
                                );
                              })()}
                            </span>
                            <span className="text-sm">
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext()
                              )}
                            </span>
                          </div>
                        ))}
                    </div>
                  </Card>
                </div>
              ))}
            </InfiniteScrollList>
          ) : (
            <div className="flex items-center justify-center h-24">
              <NoDataFound
                message="No data found"
                noDataText={text.noDataText}
              />
            </div>
          )}
        </div>
      )}
      {!isMobile && (
        <div className="space-y-4">
          {showColumnToggle && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="ml-auto">
                  {text.columns} <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {table
                  .getAllColumns()
                  .filter(
                    (column) =>
                      column.getCanHide() &&
                      (column.columnDef as ExtendedColumnDef<TData, TValue>)
                        .show !== "mobile" &&
                      (column.columnDef as ExtendedColumnDef<TData, TValue>)
                        .show !== false
                  )
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) =>
                          column.toggleVisibility(!!value)
                        }
                      >
                        {formatColumnId(column.id)}
                      </DropdownMenuCheckboxItem>
                    );
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          {isTableLoading && <LoadingSpinner />}
          <div
            className="flex-1 overflow-y-auto"
            style={{ maxHeight: adjustedMaxHeight }}
          >
            <Table className={className}>
              {!noDataMessage && (
                <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers
                        .filter((header) => {
                          const column = header.column
                            .columnDef as ExtendedColumnDef<TData, TValue>;
                          return (
                            column.show !== false && column.show !== "mobile"
                          );
                        })
                        .map((header) => {
                          return (
                            <TableHead key={header.id}>
                              {header.isPlaceholder
                                ? null
                                : flexRender(
                                    header.column.columnDef.header,
                                    header.getContext()
                                  )}
                            </TableHead>
                          );
                        })}
                    </TableRow>
                  ))}
                </TableHeader>
              )}
              <TableBody>
                {!isTableLoading && table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className={cn(
                        onRowClick && "cursor-pointer hover:bg-muted/50",
                        rowClassName && rowClassName(row)
                      )}
                      onClick={(e) => onRowClick && handleRowClick(e, row)}
                    >
                      {row
                        .getVisibleCells()
                        .filter((cell) => {
                          const column = cell.column
                            .columnDef as ExtendedColumnDef<TData, TValue>;
                          return (
                            column.show !== false && column.show !== "mobile"
                          );
                        })
                        .map((cell) => (
                          <TableCell key={cell.id}>
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </TableCell>
                        ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow disableHover>
                    <TableCell
                      colSpan={columns.length + (enableRowSelection ? 1 : 0)}
                      className="h-40 text-center"
                    >
                      <div
                        className={noDataMessage ? "py-[5rem]" : "py-[1.9rem]"}
                      >
                        <NoDataFound
                          message={
                            noDataMessage
                              ? "Please apply filters to view user data"
                              : ""
                          }
                          noDataText={text.noDataText}
                        />
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          {!disablePagination && !noDataMessage && (
            <DataTablePagination
              text={text?.pagination}
              table={table}
              onPaginationChange={onPaginationChange}
              totalCount={totalCount}
              isLoading={isTableLoading}
              pageSizeOptions={pageSizeOptions}
            />
          )}
        </div>
      )}
    </>
  );
}
