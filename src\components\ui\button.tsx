import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { Loader2 } from "lucide-react";
import { cn } from "../../lib/utils";
import { buttonVariants } from "./button-variants";
import type { VariantProps } from "class-variance-authority";

type ButtonBaseProps = {
  asChild?: boolean;
  isLoading?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
};

type ButtonProps = ButtonBaseProps &
  Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, keyof ButtonBaseProps> &
  VariantProps<typeof buttonVariants>;

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      color,
      asChild = false,
      isLoading = false,
      disabled,
      startIcon,
      endIcon,
      children,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : "button";
    const buttonClasses = cn(
      buttonVariants({ variant, size, color, className })
    );

    if (asChild) {
      return (
        <Comp
          ref={ref}
          data-slot="button"
          className={buttonClasses}
          disabled={disabled || isLoading}
          {...props}
        >
          {children}
        </Comp>
      );
    }
    return (
      <Comp
        ref={ref}
        data-slot="button"
        className={buttonClasses}
        disabled={disabled || isLoading}
        {...props}
      >
        {startIcon && startIcon}
        {children}
        {endIcon && endIcon}
        {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
      </Comp>
    );
  }
);

export { Button };
