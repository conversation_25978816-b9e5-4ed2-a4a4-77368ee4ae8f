# Azure DevOps Pipeline Setup Guide

This guide explains how to set up the Azure DevOps pipeline for the CPS Common UI library to automatically publish to Azure Artifacts.

## Prerequisites

1. **Azure DevOps Project**: Ensure you have access to the Azure DevOps project
2. **Azure Artifacts Feed**: The `cps-ui-dev` feed should already be created
3. **Repository**: The code repository should be connected to Azure DevOps

## Pipeline Setup Steps

### 1. Create the Pipeline

1. Go to your Azure DevOps project
2. Navigate to **Pipelines** > **Pipelines**
3. Click **New pipeline**
4. Select your repository source (Azure Repos Git, GitHub, etc.)
5. Select **Existing Azure Pipelines YAML file**
6. Choose the `azure-pipelines.yml` file from the repository root
7. Click **Continue** and then **Run**

### 2. Configure Service Connections (if needed)

If you plan to create GitHub releases, you'll need to set up a GitHub service connection:

1. Go to **Project Settings** > **Service connections**
2. Click **New service connection**
3. Select **GitHub**
4. Follow the authentication steps
5. Name it `github-connection` (or update the pipeline YAML accordingly)

### 3. Configure Environments

The pipeline uses an environment called `npm-packages` for deployment approvals:

1. Go to **Pipelines** > **Environments**
2. Click **New environment**
3. Name it `npm-packages`
4. Add any required approvals or checks

### 4. Verify Azure Artifacts Feed Access

Ensure the pipeline has access to publish to your artifacts feed:

1. Go to **Artifacts** > **Feeds**
2. Select your `cps-ui-dev` feed
3. Go to **Feed settings** > **Permissions**
4. Ensure the build service has **Contributor** or **Owner** permissions:
   - `[Project Name] Build Service ([Organization Name])`

## Pipeline Features

### Automatic Versioning
- **Main branch**: Uses semantic versioning (0.0.x)
- **Other branches**: Adds branch name and build ID as pre-release identifier

### Build Triggers
- Triggers on pushes to `main` and `develop` branches
- Triggers on pull requests to `main` and `develop` branches
- Excludes changes to documentation files

### Build Process
1. **Setup**: Installs Node.js and pnpm
2. **Dependencies**: Installs packages with caching for faster builds
3. **Testing**: Runs tests if available
4. **Build**: Compiles the library using rollup
5. **Publish**: Publishes to Azure Artifacts (only for main/develop branches)

### Artifacts Published
- Built library files (`dist/` folder)
- Package metadata (`package.json`)
- Documentation (`README.md`)
- License file

## Customization Options

### Change Version Strategy
Edit the `variables` section in `azure-pipelines.yml`:
```yaml
variables:
  majorVersion: '1'  # Change major version
  minorVersion: '0'  # Change minor version
```

### Change Target Branches
Edit the `trigger` and `pr` sections:
```yaml
trigger:
  branches:
    include:
      - main
      - release/*  # Add release branches
```

### Add Build Notifications
Add notification tasks or integrate with Teams/Slack for build status updates.

## Troubleshooting

### Authentication Issues
- Verify the build service has permissions to the artifacts feed
- Check that the `.npmrc` file is correctly configured in the pipeline

### Build Failures
- Check the build logs in Azure DevOps
- Verify all dependencies are correctly specified in `package.json`
- Ensure the build scripts work locally

### Publishing Issues
- Verify the package name doesn't conflict with existing packages
- Check that the version number is unique
- Ensure the artifacts feed is accessible

## Manual Publishing (Fallback)

If you need to publish manually:

1. Build the package locally:
   ```bash
   pnpm install
   pnpm run build
   ```

2. Configure npm for Azure Artifacts:
   ```bash
   npm config set registry https://pkgs.dev.azure.com/cpsAndrew/_packaging/cps-ui-dev/npm/registry/
   ```

3. Authenticate and publish:
   ```bash
   npm login
   npm publish
   ```

## Next Steps

After setting up the pipeline:

1. Test the pipeline by making a commit to the `develop` branch
2. Verify the package appears in your Azure Artifacts feed
3. Test consuming the package from the feed in another project
4. Set up any additional notifications or approvals as needed
