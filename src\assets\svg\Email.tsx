import React from "react";

interface EmailProps {
  className?: string;
  color?: string;
  size?: number;
}

const Email = ({
  className,
  color = "#3498DB",
  size = 18,
}: EmailProps): React.JSX.Element => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M16.5 8.625V11.625C16.5 14.25 15 15.375 12.75 15.375H5.25C3 15.375 1.5 14.25 1.5 11.625V6.375C1.5 3.75 3 2.625 5.25 2.625H9"
      stroke={color}
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.25 6.75L7.5975 8.625C8.37 9.24 9.6375 9.24 10.41 8.625"
      stroke={color}
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.61 2.11502L14.82 2.54251C14.925 2.75251 15.1875 2.94752 15.42 2.99252L15.705 3.03751C16.56 3.18001 16.7625 3.81001 16.1475 4.43251L15.885 4.69501C15.7125 4.87501 15.615 5.22001 15.6675 5.46001L15.705 5.61752C15.9375 6.65252 15.39 7.05001 14.49 6.51001L14.295 6.39751C14.0625 6.26251 13.6875 6.26251 13.455 6.39751L13.26 6.51001C12.3525 7.05751 11.805 6.65252 12.045 5.61752L12.0825 5.46001C12.135 5.22001 12.0375 4.87501 11.865 4.69501L11.6025 4.43251C10.9875 3.81001 11.19 3.18001 12.045 3.03751L12.33 2.99252C12.555 2.95502 12.825 2.75251 12.93 2.54251L13.14 2.11502C13.545 1.29752 14.205 1.29752 14.61 2.11502Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default Email;
