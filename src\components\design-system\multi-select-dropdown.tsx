import * as React from "react";
import { ChevronDown } from "lucide-react";
import { cn } from "../../lib/utils";
import { Button } from "../ui/button";
import {
  Command,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "../ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Checkbox } from "../ui/check-box";

interface MultiSelectDropdownProps {
  label?: string;
  options: { value: string; label: string }[];
  selected: string[];
  onChange: (selected: string[]) => void;
  placeholder?: string;
  className?: string;
  maxDisplayItems?: number;
  required?: boolean;
  selectAll?: boolean;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  name?: string;
  disableHover?: boolean;
}

export const MultiSelectDropdown = React.forwardRef<
  HTMLButtonElement,
  MultiSelectDropdownProps
>(
  (
    {
      label,
      options,
      selected,
      onChange,
      placeholder = "Select at least one",
      className,
      maxDisplayItems = 3,
      required = false,
      selectAll = true,
      error = false,
      helperText = "",
      disabled = false,
      name,
      disableHover = false,
    },
    ref
  ) => {
    const [open, setOpen] = React.useState(false);
    const [searchText, setSearchText] = React.useState("");

    const filteredOptions = React.useMemo(() => {
      if (!searchText) return options;
      return options.filter((option) =>
        option.label.toLowerCase().includes(searchText.toLowerCase())
      );
    }, [options, searchText]);

    const toggleItem = (value: string) => {
      const newSelected = selected.includes(value)
        ? selected.filter((v) => v !== value)
        : [...selected, value];
      onChange(newSelected);
    };

    const handleSelectAll = () => {
      if (selected.length === options.length) {
        onChange([]);
      } else {
        onChange(options.map((o) => o.value));
      }
    };

    const selectedLabels = options
      .filter((opt) => selected.includes(opt.value))
      .map((opt) => opt.label);

    const selectedText =
      selected.length === 0
        ? placeholder
        : selectedLabels.slice(0, maxDisplayItems).join(", ") +
          (selected.length > maxDisplayItems
            ? ` +${selected.length - maxDisplayItems} more`
            : "");

    return (
      <div className="flex flex-col gap-1.25">
        {label && (
          <span className="text-xs text-black text-left">
            {label}
            {required && <span className="ml-0.25 text-destructive">*</span>}
          </span>
        )}
        <Popover
          open={open}
          onOpenChange={(newOpen) => !disabled && setOpen(newOpen)}
        >
          <PopoverTrigger className="w-full" disabled={disabled}>
            <Button
              ref={ref}
              variant="outline"
              role="combobox"
              aria-expanded={open}
              aria-haspopup="listbox"
              aria-controls="multi-select-options"
              aria-label={label}
              aria-required={required}
              aria-invalid={error}
              aria-describedby={error ? `${name}-error` : undefined}
              name={name}
              disabled={disabled}
              className={cn(
                "w-full hover:text-current text-left bg-white",
                className,
                error
                  ? "border-destructive focus:border-destructive focus:ring-destructive"
                  : "",
                disabled ? "opacity-50 cursor-not-allowed" : "",
                disableHover ? "hover:bg-white" : "hover:bg-transparent"
              )}
            >
              <div
                className="flex gap-1 items-center text-xs text-left min-w-0 w-full p-0"
                aria-hidden="true"
              >
                <span
                  className={cn(
                    "truncate w-full text-black",
                    selected.length === 0 && "text-[var(--placeholder)]"
                  )}
                >
                  {selectedText}
                </span>
              </div>
              <ChevronDown
                className={cn(
                  "ml-2 h-5 w-5 shrink-0 transition-transform duration-200 text-[#696969]",
                  open && "rotate-180"
                )}
                aria-hidden="true"
              />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className="w-full p-0"
            style={{ width: "var(--radix-popover-trigger-width)" }}
            align="start"
            role="listbox"
            id="multi-select-options"
          >
            <Command shouldFilter={false}>
              <CommandInput
                placeholder="Search"
                value={searchText}
                onValueChange={setSearchText}
                className=""
                aria-label="Search options"
              />
              <CommandList>
                <CommandGroup>
                  <div className="max-h-[200px] overflow-y-auto">
                    {selectAll && (
                      <CommandItem
                        onSelect={() => handleSelectAll()}
                        className="flex items-center gap-2 data-[selected=true]:bg-muted data-[selected=true]:text-muted-foreground"
                        role="option"
                        aria-selected={selected.length === options.length}
                      >
                        <Checkbox
                          checked={selected.length === options.length}
                          onCheckedChange={handleSelectAll}
                          className="mr-2 h-4 w-4"
                          aria-label="Select all options"
                        />
                        <span>All</span>
                      </CommandItem>
                    )}
                    {filteredOptions.map((option) => (
                      <CommandItem
                        key={option.value}
                        onSelect={() => toggleItem(option.value)}
                        className="flex items-center gap-2 data-[selected=true]:bg-muted data-[selected=true]:text-muted-foreground"
                        role="option"
                        aria-selected={selected.includes(option.value)}
                      >
                        <Checkbox
                          checked={selected.includes(option.value)}
                          onCheckedChange={() => toggleItem(option.value)}
                          className="mr-2 h-4 w-4"
                          aria-label={`Select ${option.label}`}
                        />
                        <span>{option.label}</span>
                      </CommandItem>
                    ))}
                  </div>
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
        {error && (
          <span
            id={`${name}-error`}
            className="text-xs text-destructive"
            role="alert"
          >
            {helperText}
          </span>
        )}
      </div>
    );
  }
);

MultiSelectDropdown.displayName = "MultiSelectDropdown";
