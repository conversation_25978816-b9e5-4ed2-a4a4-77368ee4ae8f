{"name": "common-ui", "version": "0.0.23", "description": "A modern React component library with a beautiful design system", "type": "module", "main": "./dist/index.js", "module": "./dist/index.esm.js", "types": "./dist/types/index.d.ts", "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/types/index.d.ts"}, "./icons": {"import": "./dist/icons.esm.js", "require": "./dist/icons.js", "types": "./dist/types/assets/svg/index.d.ts"}, "./styles": {"import": "./dist/index.esm.css", "require": "./dist/index.css", "types": "./dist/types/index.d.ts"}}, "files": ["dist", "README.md", "LICENSE"], "scripts": {"storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "prepare": "npm run build", "build": "npm run build:rollup && npm run build:types", "build:rollup": "rollup -c", "build:types": "npx tsc -p tsconfig.app.json --emitDeclarationOnly", "version:check": "npm version --help", "release": "npm run release:patch", "release:patch": "npm run build && npm version patch && npm publish", "release:minor": "npm run build && npm version minor && npm publish", "release:major": "npm run build && npm version major && npm publish", "release:beta": "npm run build && npm version prerelease --preid=beta && npm publish --tag beta"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-infinite-scroll-hook": "^6.0.0", "react-toastify": "^11.0.5", "tailwind-merge": "^3.3.0", "vaul": "^1.1.2"}, "devDependencies": {"@chromatic-com/storybook": "3", "@eslint/js": "^9.25.0", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-image": "^3.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-typescript": "^12.1.2", "@storybook/addon-essentials": "8.6.14", "@storybook/addon-onboarding": "8.6.14", "@storybook/blocks": "8.6.14", "@storybook/experimental-addon-test": "8.6.14", "@storybook/react": "8.6.14", "@storybook/react-vite": "8.6.14", "@storybook/test": "8.6.14", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/vite": "^4.1.7", "@types/node": "^22.15.21", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.4.1", "@vitest/browser": "^3.1.4", "@vitest/coverage-v8": "^3.1.4", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-storybook": "^0.12.0", "globals": "^16.0.0", "playwright": "^1.52.0", "postcss": "^8.5.3", "postcss-preset-env": "^10.2.3", "rollup": "^4.41.1", "rollup-plugin-esbuild": "^6.2.1", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "storybook": "8.6.14", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.1.4"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0", "react-router-dom": "^7.6.1"}, "keywords": ["react", "components", "ui", "design-system", "tailwindcss"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/common-ui"}}