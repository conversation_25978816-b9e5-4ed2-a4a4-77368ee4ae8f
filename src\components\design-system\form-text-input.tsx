import React, { forwardRef } from "react";
import { Input as ShadcnInput } from "../ui/input";
import { cn } from "../../lib/utils";

export interface FormTextInputProps
  extends React.ComponentProps<typeof ShadcnInput> {
  label?: string;
  required?: boolean;
  helperText?: string;
  error?: boolean;
}

export const FormTextInput = forwardRef<HTMLInputElement, FormTextInputProps>(
  ({ className, id, label, required, helperText, error, ...props }, ref) => {
    const inputId = id || props.name;
    return (
      <div className="flex flex-col space-y-1">
        {label && (
          <span className="text-xs">
            {label}
            <span className="ml-0.25">
              {required && <span className="text-destructive">*</span>}
            </span>
          </span>
        )}
        <ShadcnInput
          id={inputId}
          ref={ref}
          className={cn(
            className,
            "!text-xs",
            error
              ? "border-destructive focus:border-destructive focus:ring-destructive"
              : ""
          )}
          {...props}
        />
        {helperText && <p className="text-xs text-destructive">{helperText}</p>}
      </div>
    );
  }
);

FormTextInput.displayName = "FormTextInput";
