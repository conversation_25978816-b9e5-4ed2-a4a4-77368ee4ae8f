import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import PhoneInputField from "../components/design-system/phone-input-field";

const meta: Meta<typeof PhoneInputField> = {
  title: "Design System/PhoneInputField",
  component: PhoneInputField,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: { control: "text" },
    placeholder: { control: "text" },
    value: { control: "text" },
    disabled: { control: "boolean" },
    required: { control: "boolean" },
    helperText: { control: "text" },
    onChange: { action: "changed" },
  },
};

export default meta;
type Story = StoryObj<typeof PhoneInputField>;

export const Default: Story = {
  args: {
    label: "Phone Number",
    placeholder: "Enter Phone Number",
  },
};

export const WithValue: Story = {
  args: {
    label: "Phone Number",
    value: "************",
  },
};

export const Required: Story = {
  args: {
    label: "Phone Number",
    required: true,
  },
};

export const WithHelperText: Story = {
  args: {
    label: "Phone Number",
    helperText: "Please enter a valid phone number",
  },
};

export const Disabled: Story = {
  args: {
    label: "Phone Number",
    value: "************",
    disabled: true,
  },
};

export const WithLongExtension: Story = {
  args: {
    label: "Phone Number",
    value: "************ ext 12345",
  },
};
