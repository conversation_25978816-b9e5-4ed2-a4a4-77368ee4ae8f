import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { SearchMultiSelectDropdown } from "../components/design-system/search-multi-select-dropdown";
declare const meta: Meta<typeof SearchMultiSelectDropdown>;
export default meta;
type Story = StoryObj<typeof SearchMultiSelectDropdown>;
export declare const Default: Story;
export declare const WithSelectedItems: Story;
export declare const Required: Story;
export declare const AllSelected: Story;
export declare const CustomMaxDisplayItems: Story;
//# sourceMappingURL=search-multi-select-dropdown.stories.d.ts.map