import type { ChangeEvent } from "react";
import { Textarea } from "../ui/textarea";
interface TextAreaFieldProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (e: ChangeEvent<HTMLTextAreaElement>) => void;
  disabled?: boolean;
  required?: boolean;
  error?: boolean;
  helperText?: string;
}

export default function TextAreaField({
  label,
  placeholder = "",
  value = "",
  onChange,
  disabled = false,
  required = false,
  error = false,
  helperText,
}: TextAreaFieldProps) {
  return (
    <div className="flex flex-col gap-1.25">
      {label && (
        <span className="text-xs">
          {label}
          <span className="ml-0.25">
            {required && <span className="text-destructive">*</span>}
          </span>
        </span>
      )}
      <Textarea
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        disabled={disabled}
        required={required}
        className={`!text-xs h-9  ${
          error
            ? "border-destructive focus-visible:border-destructive selection:bg-destructive focus-visible:ring-destructive/20"
            : ""
        }`}
      />
      {error && <span className="text-xs text-destructive">{helperText}</span>}
    </div>
  );
}
