import type { OnChangeFn, SortingState } from "@tanstack/react-table";
type fileldType = {
    sortBy: string;
    sortOrder: "asc" | "desc";
    label: string;
    type?: "number" | "string";
};
interface DataTableColumnHeaderProps {
    title?: string;
    className?: string;
    headerTitle?: React.ReactNode;
    fields: fileldType[];
    onSelection: OnChangeFn<SortingState>;
    sorting: SortingState;
}
export declare function MultiFieldHeader({ title, headerTitle, className, fields, onSelection, sorting, }: DataTableColumnHeaderProps): import("react/jsx-runtime").JSX.Element;
export {};
//# sourceMappingURL=data-table-multi-field-header.d.ts.map