import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../ui/alert-dialog";
import { X } from "lucide-react";
import { Button } from "../ui/button";
import { useConfirm } from "../../hooks/use-confirm";
import { useIsMobile } from "../../hooks/use-mobile";
import DrawerModal from "./drawer-modal";

export default function ConfirmationPopup() {
  const { confirmOptions } = useConfirm();
  const isMobile = useIsMobile();

  if (!confirmOptions) return null;

  const {
    title,
    message,
    primaryButtonText,
    secondaryButtonText,
    onPrimaryButtonClick,
    onSecondaryButtonClick,
  } = confirmOptions;

  return (
    <>
      {!isMobile && (
        <AlertDialog open={true}>
          <AlertDialogContent className="p-0 rounded-xl !max-w-[400px]">
            <AlertDialogHeader className="flex flex-col">
              <AlertDialogTitle className="px-5 py-2 flex justify-between items-center bg-[#EEEEEE] rounded-t-xl">
                <span className="font-medium text-[0.875rem]">{title}</span>
                <Button
                  variant="ghost"
                  size="icon"
                  className="bg-transparent shadow-none hover:text-black/70"
                  onClick={onSecondaryButtonClick}
                >
                  <X className="w-4 h-4" />
                </Button>
              </AlertDialogTitle>
              <AlertDialogDescription className="px-5 py-3 text-[0.875rem]">
                {message}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="px-5 pb-5 flex gap-2 !justify-start text-sm font-normal">
              <Button
                color="destructive"
                variant="contained"
                className="font-medium text-[0.75rem]"
                onClick={onPrimaryButtonClick}
              >
                {primaryButtonText}
              </Button>
              <Button
                variant="outline"
                color="destructive"
                className="font-medium text-[0.75rem]"
                onClick={onSecondaryButtonClick}
              >
                {secondaryButtonText}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
      {isMobile && (
        <DrawerModal popUpTitle={title} open={true}>
          <div className="flex flex-col gap-6">
            <div className="text-[0.875rem]">{message}</div>
            <div className="flex gap-2">
              <Button
                color="destructive"
                variant="contained"
                className="font-medium flex-1"
                onClick={onPrimaryButtonClick}
              >
                {primaryButtonText}
              </Button>
              <Button
                variant="outline"
                color="destructive"
                className="font-medium flex-1"
                onClick={onSecondaryButtonClick}
              >
                {secondaryButtonText}
              </Button>
            </div>
          </div>
        </DrawerModal>
      )}
    </>
  );
}
