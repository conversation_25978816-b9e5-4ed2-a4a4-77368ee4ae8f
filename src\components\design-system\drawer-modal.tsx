import React, { type FC } from "react";
import {
  Drawer,
  Drawer<PERSON>ontent,
  DrawerFooter,
  DrawerTrigger,
} from "../../components/ui/drawer";
import { cn } from "../../lib/utils";

interface DrawerModalProps {
  title?: string;
  popUpTitle?: string;
  trigger?: React.ReactNode;
  children: React.ReactNode;
  footer?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  contentClassName?: string;
}

const DrawerModal: FC<DrawerModalProps> = ({
  title,
  popUpTitle,
  trigger,
  children,
  footer,
  open,
  onOpenChange,
  contentClassName,
}) => {
  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      {trigger && <DrawerTrigger>{trigger}</DrawerTrigger>}
      <DrawerContent
        title={title}
        popUpTitle={popUpTitle}
        className={cn("overflow-y-auto", contentClassName)}
      >
        <div className="p-5">{children}</div>
        {footer && <DrawerFooter>{footer}</DrawerFooter>}
      </DrawerContent>
    </Drawer>
  );
};

export default DrawerModal;
