import React from "react";

interface EyeProps {
  className?: string;
  color?: string;
  size?: number;
}

const Eye = ({
  className,
  color = "#3498DB",
  size = 18,
}: EyeProps): React.JSX.Element => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M11.6849 9C11.6849 10.485 10.4849 11.685 8.99994 11.685C7.51494 11.685 6.31494 10.485 6.31494 9C6.31494 7.515 7.51494 6.315 8.99994 6.315C10.4849 6.315 11.6849 7.515 11.6849 9Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.99988 15.2025C11.6474 15.2025 14.1149 13.6425 15.8324 10.9425C16.5074 9.88498 16.5074 8.10748 15.8324 7.04998C14.1149 4.34998 11.6474 2.78998 8.99988 2.78998C6.35238 2.78998 3.88488 4.34998 2.16738 7.04998C1.49238 8.10748 1.49238 9.88498 2.16738 10.9425C3.88488 13.6425 6.35238 15.2025 8.99988 15.2025Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default Eye;
