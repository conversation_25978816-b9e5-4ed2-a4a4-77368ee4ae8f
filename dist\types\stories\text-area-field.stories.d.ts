import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import TextAreaField from "../components/design-system/text-area-field";
declare const meta: Meta<typeof TextAreaField>;
export default meta;
type Story = StoryObj<typeof TextAreaField>;
export declare const Default: Story;
export declare const WithValue: Story;
export declare const Required: Story;
export declare const WithWarning: Story;
export declare const Disabled: Story;
//# sourceMappingURL=text-area-field.stories.d.ts.map