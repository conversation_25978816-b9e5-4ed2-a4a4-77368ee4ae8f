import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { PaginatedSearchMultiSelectDropdown } from "../components/design-system/paginated-search-multi-select-dropdown";
declare const meta: Meta<typeof PaginatedSearchMultiSelectDropdown>;
export default meta;
type Story = StoryObj<typeof PaginatedSearchMultiSelectDropdown>;
export declare const Default: Story;
export declare const WithSelectedItems: Story;
export declare const Required: Story;
export declare const Loading: Story;
export declare const CustomMaxDisplayItems: Story;
//# sourceMappingURL=paginated-search-multi-select-dropdown.stories.d.ts.map