import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Avatar, AvatarFallback } from "../ui/avatar";
import { SidebarTrigger, useSidebar } from "../design-system/sidebar";

interface HeaderProps {
  user: string;
  userFallback: string;
  headerImg: string;
  userDropdownItems?: {
    label: string;
    icon?: React.ReactNode;
    onClick?: () => void;
  }[];
  sidebarExpanded: string;
  sidebarCollapsed: string;
}

export function Header({
  user,
  userFallback,
  headerImg,
  userDropdownItems,
  sidebarExpanded,
  sidebarCollapsed,
}: HeaderProps) {
  const { visible } = useSidebar();

  return (
    <header className="flex items-center justify-between px-5 py-3 bg-background">
      <div className="flex gap-3 items-center">
        {!visible && (
          <SidebarTrigger
            className="-ml-1"
            sidebarExpanded={sidebarExpanded}
            sidebarCollapsed={sidebarCollapsed}
          />
        )}
        <img
          className="ring-background rounded-full ring-1"
          src={headerImg}
          width={40}
          height={40}
          alt="header-img"
        />
        <span className="font-medium text-sm">{user}</span>
      </div>
      <div className="flex items-center gap-2">
        <DropdownMenu>
          <DropdownMenuTrigger>
            <Avatar className="cursor-pointer">
              <AvatarFallback>{userFallback}</AvatarFallback>
            </Avatar>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-20">
            {userDropdownItems?.map((item) => (
              <DropdownMenuItem key={item.label} onClick={item.onClick}>
                {item.icon}
                <span>{item.label}</span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
