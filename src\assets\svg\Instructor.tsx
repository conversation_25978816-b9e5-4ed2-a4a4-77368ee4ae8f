import React from "react";

interface InstructorProps {
  className?: string;
  color?: string;
  size?: number;
}

const Instructor = ({
  className,
  color = "black",
  size = 18,
}: InstructorProps): React.JSX.Element => (
  <svg
    className={className}
    width={size}
    height={size}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.5 12.7125H4.6575C2.13 12.7125 1.5 12.0825 1.5 9.55502V5.05502C1.5 2.52752 2.13 1.89752 4.6575 1.89752H12.555C15.0825 1.89752 15.7125 2.52752 15.7125 5.05502"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.5 16.1025V12.7125"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1.5 9.71252H7.5"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.05499 16.1025H7.49999"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.5 9.6V13.8825C16.5 15.66 16.0575 16.1025 14.28 16.1025H11.6175C9.84002 16.1025 9.39752 15.66 9.39752 13.8825V9.6C9.39752 7.8225 9.84002 7.38 11.6175 7.38H14.28C16.0575 7.38 16.5 7.8225 16.5 9.6Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.9334 13.6875H12.9401"
      stroke="#161618"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default Instructor;
