import * as React from "react";

interface WarningIconProps {
  color?: string;
  className?: string;
  size?: number;
}

const WarningIcon = ({
  color = "#EB062B",
  className,
  size = 12,
}: WarningIconProps): React.JSX.Element => {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 12 11"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.54 7.665L7.5 1.285C7.16 0.795 6.595 0.5 6 0.5C5.405 0.5 4.84 0.79 4.485 1.3L0.465003 7.655C-0.0449976 8.385 -0.139998 9.26 0.215002 9.935C0.565003 10.61 1.3 10.995 2.22 10.995H9.78C10.705 10.995 11.435 10.61 11.785 9.935C12.135 9.26 12.04 8.39 11.54 7.665ZM5.5 3.5C5.5 3.225 5.725 3 6 3C6.275 3 6.5 3.225 6.5 3.5V6.5C6.5 6.775 6.275 7 6 7C5.725 7 5.5 6.775 5.5 6.5V3.5ZM6 9.5C5.585 9.5 5.25 9.165 5.25 8.75C5.25 8.335 5.585 8 6 8C6.415 8 6.75 8.335 6.75 8.75C6.75 9.165 6.415 9.5 6 9.5Z"
        fill={color}
      />
    </svg>
  );
};

export default WarningIcon;
