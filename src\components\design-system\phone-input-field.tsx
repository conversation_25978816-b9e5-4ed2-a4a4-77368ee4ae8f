import React from "react";
import { Input } from "../ui/input";

interface PhoneInputFieldProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (formattedValue: string) => void;
  disabled?: boolean;
  required?: boolean;
  helperText?: string;
  error?: boolean;
}

export default function PhoneInputField({
  label,
  placeholder = "************ ext 1234",
  value = "",
  onChange,
  disabled = false,
  required = false,
  helperText,
  error,
}: PhoneInputFieldProps) {
  const formatPhoneWithExtension = (input: string) => {
    const digits = input.replace(/\D/g, "");
    const phone = digits.substring(0, 10);
    const extension = digits.substring(10);

    let formatted = "";

    if (phone.length > 0) {
      formatted += phone.substring(0, 3);
      if (phone.length > 3) {
        formatted += "-" + phone.substring(3, 6);
      }
      if (phone.length > 6) {
        formatted += "-" + phone.substring(6, 10);
      }
    }

    if (extension.length > 0) {
      formatted += " ext " + extension;
    }

    return formatted;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneWithExtension(e.target.value);
    onChange?.(formatted);
  };

  return (
    <div className="flex flex-col gap-1.25 w-full">
      {label && (
        <span className="text-xs">
          {label}
          {required && <span className="text-destructive ml-0.25">*</span>}
        </span>
      )}
      <Input
        placeholder={placeholder}
        value={value}
        onChange={handleInputChange}
        disabled={disabled}
        required={required}
        className={`!text-xs h-9 ${
          error
            ? "border-destructive focus-visible:border-destructive selection:bg-destructive focus-visible:ring-destructive/20"
            : ""
        }`}
      />
      {error && <span className="text-xs text-destructive">{helperText}</span>}
    </div>
  );
}
