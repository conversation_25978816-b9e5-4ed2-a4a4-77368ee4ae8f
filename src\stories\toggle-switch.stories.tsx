import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import ToggleSwitch from "../components/design-system/toggle-switch";

const meta: Meta<typeof ToggleSwitch> = {
  title: "Design System/ToggleSwitch",
  component: ToggleSwitch,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: { control: "text" },
    value: { control: "boolean" },
    required: { control: "boolean" },
    option: { control: "text" },
    onChange: { action: "changed" },
  },
};

export default meta;
type Story = StoryObj<typeof ToggleSwitch>;

export const Default: Story = {
  args: {
    label: "Toggle Switch",
    option: "Active",
  },
};

export const Checked: Story = {
  args: {
    label: "Toggle Switch",
    option: "Active",
    value: true,
  },
};

export const Required: Story = {
  args: {
    label: "Required Toggle",
    option: "Active",
    required: true,
  },
};

export const CustomOption: Story = {
  args: {
    label: "Custom Toggle",
    option: "Enabled",
  },
};
