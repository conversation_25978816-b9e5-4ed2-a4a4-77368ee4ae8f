import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import ToggleSwitch from "../components/design-system/toggle-switch";
declare const meta: Meta<typeof ToggleSwitch>;
export default meta;
type Story = StoryObj<typeof ToggleSwitch>;
export declare const Default: Story;
export declare const Checked: Story;
export declare const Required: Story;
export declare const CustomOption: Story;
//# sourceMappingURL=toggle-switch.stories.d.ts.map