import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Toaster from "../components/design-system/toaster";

const meta: Meta<typeof Toaster> = {
  title: "Design System/Toaster",
  component: Toaster,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    data: {
      control: "object",
      description: "Toast data including type, title, and content",
    },
    closeToast: { action: "closeToast" },
    toastProps: {
      control: "object",
      description: "Toast properties including autoClose duration",
    },
  },
};

export default meta;
type Story = StoryObj<typeof Toaster>;

export const Success: Story = {
  args: {
    data: {
      type: "success",
      title: "Success",
      content: "Operation completed successfully",
    },
  },
};

export const Warning: Story = {
  args: {
    data: {
      type: "warning",
      title: "Warning",
      content: "Please review your changes before proceeding",
    },
  },
};

export const Error: Story = {
  args: {
    data: {
      type: "error",
      title: "Error",
      content: "An error occurred while processing your request",
    },
  },
};

export const Info: Story = {
  args: {
    data: {
      type: "info",
      title: "Information",
      content: "Here's some important information for you",
    },
  },
};
