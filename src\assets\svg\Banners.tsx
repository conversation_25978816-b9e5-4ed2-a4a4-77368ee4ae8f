import * as React from "react";

interface BannersProps {
  className?: string;
  color?: string;
  size?: number;
}

const Banners = ({
  className,
  color = "none",
  size = 18,
}: BannersProps): React.JSX.Element => (
  <svg
    className={className}
    width={size}
    height={size}
    viewBox="0 0 18 18"
    fill={color}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.86249 1.5V16.5"
      stroke="white"
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.86249 3H12.2625C14.2875 3 14.7375 4.125 13.3125 5.55L12.4125 6.45C11.8125 7.05 11.8125 8.025 12.4125 8.55L13.3125 9.45C14.7375 10.875 14.2125 12 12.2625 12H3.86249"
      stroke="white"
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default Banners;
