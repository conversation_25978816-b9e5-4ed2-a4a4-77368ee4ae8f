import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Navbar from "../components/design-system/navbar";
declare const meta: Meta<typeof Navbar>;
export default meta;
type Story = StoryObj<typeof Navbar>;
export declare const Default: Story;
export declare const WithBackButton: Story;
export declare const WithPrimaryButton: Story;
export declare const WithSecondaryButton: Story;
export declare const Complete: Story;
export declare const CustomButtons: Story;
//# sourceMappingURL=Navbar.stories.d.ts.map