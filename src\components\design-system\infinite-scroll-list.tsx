import useInfiniteScroll from 'react-infinite-scroll-hook';

interface InfiniteScrollListProps {
  hasMore: boolean;
  loadMore: () => void;
  isLoading: boolean;
  error?: Error | string | null | undefined;
  children: React.ReactNode;
  // isElementInTable?: boolean;
}

const InfiniteScrollList = ({
  hasMore,
  loadMore,
  isLoading,
  error,
  children,
  // isElementInTable = false,
}: InfiniteScrollListProps) => {
  const [sentryRef] = useInfiniteScroll({
    loading: isLoading,
    hasNextPage: hasMore,
    onLoadMore: loadMore,
    disabled: !!error,
    rootMargin: '0px 0px 400px 0px',
  });

  return (
    <>
      {children}
      {hasMore && !isLoading && <div ref={sentryRef} className="h-20" />}
    </>
  );
};

export default InfiniteScrollList;
