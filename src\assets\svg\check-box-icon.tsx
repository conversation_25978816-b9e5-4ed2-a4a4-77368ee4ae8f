interface CheckboxIconProps {
  checkedColor?: string;
}

const CheckboxIcon = ({ checkedColor = "#EB062B" }: CheckboxIconProps) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11 0C13.7614 0 16 2.23858 16 5V11C16 13.7614 13.7614 16 11 16H5C2.23858 16 0 13.7614 0 11V5C0 2.23858 2.23858 0 5 0H11ZM11.3271 5.22266C11.03 4.92561 10.5482 4.92578 10.251 5.22266L6.59082 8.88184L5.2998 7.59082C5.00265 7.29376 4.51989 7.29389 4.22266 7.59082C3.92554 7.88813 3.92541 8.37082 4.22266 8.66797L5.73828 10.1807C5.96348 10.4081 6.27077 10.5362 6.59082 10.5361C6.91047 10.537 7.21783 10.4101 7.44336 10.1836L11.3271 6.29883C11.6241 6.00149 11.6244 5.51975 11.3271 5.22266Z"
        fill={checkedColor}
      />
    </svg>
  );
};

export default CheckboxIcon;
