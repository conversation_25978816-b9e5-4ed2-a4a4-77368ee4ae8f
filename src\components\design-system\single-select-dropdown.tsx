import * as React from "react";
import { ChevronDown } from "lucide-react";
import { cn } from "../../lib/utils";
import { Button } from "../../components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "../../components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../../components/ui/popover";

export interface SingleSelectDropdownProps {
  searchable?: boolean;
  label?: string;
  /** current selected value */
  value: string | undefined;
  /** onValueChange handler */
  onChange: (value: string) => void;
  options: { value: string; label: string }[];
  placeholder?: string;
  className?: string;
  required?: boolean;
  name?: string;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  renderValue?: (
    option: { value: string; label: string } | undefined
  ) => React.ReactNode;
  renderOption?: (option: { value: string; label: string }) => React.ReactNode;
  icon?: React.ReactNode;
}

/**
 * ForwardRef component so it can be used inside RHF Controller.
 * Ref is attached to the trigger button for focus-on-error.
 */
export const SingleSelectDropdown = React.forwardRef<
  HTMLButtonElement,
  SingleSelectDropdownProps
>(
  ({
    searchable,
    label,
    value,
    onChange,
    options,
    placeholder = "Select one",
    className,
    required = false,
    name,
    error,
    helperText,
    disabled = false,
    renderValue,
    renderOption,
    icon,
  }) => {
    const [open, setOpen] = React.useState(false);
    const [searchQuery, setSearchQuery] = React.useState("");

    const handleSelect = (val: string) => {
      onChange(val);
      setOpen(false);
      setSearchQuery("");
    };

    const filteredOptions = React.useMemo(() => {
      if (!searchQuery) return options;
      return options.filter((option) =>
        option.label.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }, [options, searchQuery]);

    return (
      <div className="flex flex-col gap-1.25">
        {label && (
          <span className="text-xs">
            {label}
            {required && <span className="ml-0.25 text-destructive">*</span>}
          </span>
        )}
        <Popover
          open={open}
          onOpenChange={(newOpen) => {
            if (!newOpen) {
              setSearchQuery("");
            }
            if (!disabled) {
              setOpen(newOpen);
            }
          }}
        >
          <PopoverTrigger className="w-full" disabled={disabled}>
            <Button
              variant="outline"
              aria-expanded={open}
              name={name}
              disabled={disabled}
              className={cn(
                "w-full hover:bg-transparent hover:text-current text-left",
                className,
                error
                  ? "border-destructive focus:border-destructive focus:ring-destructive"
                  : "",
                disabled ? "opacity-50 cursor-not-allowed" : ""
              )}
            >
              {icon && <div className="mr-2">{icon}</div>}
              <div className="flex gap-1 items-center text-xs text-left min-w-0 w-full p-0">
                {value ? (
                  renderValue ? (
                    renderValue(
                      options.find((option) => option.value === value)
                    )
                  ) : (
                    <span className="truncate w-full">
                      {options.find((option) => option.value === value)
                        ?.label ?? value}
                    </span>
                  )
                ) : (
                  <span className="text-[var(--placeholder)] truncate w-full">
                    {placeholder}
                  </span>
                )}
              </div>
              <ChevronDown
                className={cn(
                  "ml-2 h-5 w-5 shrink-0 transition-transform duration-200 text-[#696969]",
                  open && "rotate-180"
                )}
              />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            style={{ width: "var(--radix-popover-trigger-width)" }}
            align="start"
            className="w-full p-0 max-h-[12.5rem]"
            data-slot="dropdown-menu-content"
          >
            <Command>
              {searchable && (
                <CommandInput
                  placeholder="Search"
                  value={searchQuery}
                  onValueChange={setSearchQuery}
                  className=""
                />
              )}
              <CommandList>
                <CommandEmpty>No options found.</CommandEmpty>
                <CommandGroup>
                  {filteredOptions.map((option) => {
                    return (
                      <CommandItem
                        data-slot="dropdown-menu-item"
                        key={option.value}
                        onSelect={() => handleSelect(option.value)}
                        className={cn(
                          "flex items-center gap-2 data-[selected=true]:bg-muted data-[selected=true]:text-muted-foreground ",
                          value === option.value &&
                            "bg-muted text-muted-foreground"
                        )}
                      >
                        {renderOption ? (
                          renderOption(option)
                        ) : (
                          <span>{option.label}</span>
                        )}
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
        {error && (
          <span className="text-xs text-destructive">{helperText}</span>
        )}
      </div>
    );
  }
);

SingleSelectDropdown.displayName = "SingleSelectDropdown";
