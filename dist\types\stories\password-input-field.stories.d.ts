import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { PasswordInput } from "../components/design-system/form-password-input";
declare const meta: Meta<typeof PasswordInput>;
export default meta;
type Story = StoryObj<typeof PasswordInput>;
export declare const Default: Story;
export declare const WithValue: Story;
export declare const Required: Story;
export declare const WithError: Story;
export declare const Disabled: Story;
//# sourceMappingURL=password-input-field.stories.d.ts.map