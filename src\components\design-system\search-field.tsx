import { useId } from "react";
import { Search } from "lucide-react";
import { Input } from "../ui/input";

interface SearchFieldProps {
  ref?: React.Ref<HTMLInputElement>;
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  required?: boolean;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onFocus?: () => void;
  autoFocus?: boolean;
}

export default function SearchField({
  autoFocus,
  ref,
  label,
  placeholder = "",
  value,
  onChange,
  disabled = false,
  required = false,
  onKeyDown,
  onFocus,
}: SearchFieldProps) {
  const id = useId();

  return (
    <div className="flex flex-col gap-1.25 pointer-events-auto ">
      {label && (
        <span className="text-xs">
          {label}
          <span className="ml-0.25">
            {required && <span className="text-[#EB062B]">*</span>}
          </span>
        </span>
      )}
      <div className="relative">
        <Input
          id={id}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          disabled={disabled}
          required={required}
          autoFocus={autoFocus}
          ref={ref}
          onKeyDown={onKeyDown}
          onFocus={onFocus}
          className="peer ps-9 !text-xs bg-white"
        />
        <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3 text-muted-foreground peer-disabled:opacity-50">
          <Search size={16} aria-hidden="true" className="text-black" />
        </div>
      </div>
    </div>
  );
}
