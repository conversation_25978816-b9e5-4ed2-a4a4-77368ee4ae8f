import React from "react";
interface CloseProps {
  color?: string;
  size?: number;
  className?: string;
}

const Close = ({
  color = "#FF5454",
  size = 16,
  className,
}: CloseProps): React.JSX.Element => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <circle cx="8" cy="8" r="8" fill={color} />
    <path
      d="M10.8524 10.1473C10.899 10.1935 10.9361 10.2485 10.9614 10.3091C10.9867 10.3697 10.9999 10.4347 11 10.5003C11.0001 10.566 10.9873 10.6311 10.9623 10.6917C10.9372 10.7524 10.9004 10.8076 10.854 10.854C10.8075 10.9004 10.7524 10.9372 10.6917 10.9623C10.631 10.9874 10.5659 11.0002 10.5003 11.0001C10.4346 10.9999 10.3696 10.9868 10.309 10.9615C10.2485 10.9362 10.1935 10.8991 10.1472 10.8525L7.99923 8.70696L5.85121 10.8525C5.7577 10.946 5.63087 10.9985 5.49863 10.9985C5.36639 10.9985 5.23956 10.946 5.14605 10.8525C5.05253 10.759 5 10.6321 5 10.4999C5 10.3676 5.05253 10.2408 5.14605 10.1473L7.29156 7.99929L5.14605 5.85128C5.05253 5.75776 5 5.63094 5 5.49869C5 5.36645 5.05253 5.23962 5.14605 5.14611C5.23956 5.05259 5.36639 5.00006 5.49863 5.00006C5.63087 5.00006 5.7577 5.05259 5.85121 5.14611L7.99923 7.29162L10.1472 5.14611C10.2408 5.05259 10.3676 5.00006 10.4998 5.00006C10.6321 5.00006 10.7589 5.05259 10.8524 5.14611C10.9459 5.23962 10.9985 5.36645 10.9985 5.49869C10.9985 5.63094 10.9459 5.75776 10.8524 5.85128L8.7069 7.99929L10.8524 10.1473Z"
      fill="none"
    />
    <path
      d="M10.8524 10.1472C10.899 10.1935 10.9361 10.2485 10.9614 10.309C10.9867 10.3696 10.9999 10.4346 11 10.5003C11.0001 10.5659 10.9873 10.631 10.9623 10.6917C10.9372 10.7524 10.9004 10.8075 10.854 10.854C10.8075 10.9004 10.7524 10.9372 10.6917 10.9623C10.631 10.9873 10.5659 11.0001 10.5003 11C10.4346 10.9999 10.3696 10.9867 10.309 10.9614C10.2485 10.9361 10.1935 10.899 10.1472 10.8524L7.99923 8.7069L5.85121 10.8524C5.7577 10.9459 5.63087 10.9985 5.49863 10.9985C5.36639 10.9985 5.23956 10.9459 5.14605 10.8524C5.05253 10.7589 5 10.6321 5 10.4998C5 10.3676 5.05253 10.2408 5.14605 10.1472L7.29156 7.99923L5.14605 5.85121C5.05253 5.7577 5 5.63087 5 5.49863C5 5.36639 5.05253 5.23956 5.14605 5.14605C5.23956 5.05253 5.36639 5 5.49863 5C5.63087 5 5.7577 5.05253 5.85121 5.14605L7.99923 7.29156L10.1472 5.14605C10.2408 5.05253 10.3676 5 10.4998 5C10.6321 5 10.7589 5.05253 10.8524 5.14605C10.9459 5.23956 10.9985 5.36639 10.9985 5.49863C10.9985 5.63087 10.9459 5.7577 10.8524 5.85121L8.7069 7.99923L10.8524 10.1472Z"
      fill="white"
    />
  </svg>
);

export default Close;
