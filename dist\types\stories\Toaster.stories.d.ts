import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Toaster from "../components/design-system/toaster";
declare const meta: Meta<typeof Toaster>;
export default meta;
type Story = StoryObj<typeof Toaster>;
export declare const Success: Story;
export declare const Warning: Story;
export declare const Error: Story;
export declare const Info: Story;
//# sourceMappingURL=Toaster.stories.d.ts.map