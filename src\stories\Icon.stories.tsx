import type { Meta, StoryObj } from "@storybook/react";
import * as Icons from "../assets/svg/index";

const meta: Meta = {
  title: "Icons/All Icons",
};

export default meta;
type Story = StoryObj;

const IconGrid = () => {
  const iconComponents = Object.entries(Icons).filter(
    ([name]) => name !== "default"
  );

  return (
    <div className="grid grid-cols-10 gap-8 p-8 grid-flow-row">
      {iconComponents
        .filter(([name]) => name !== "ToastIcon")
        .map(([name, Icon]) => (
          <div key={name} className="flex flex-col items-center gap-2">
            <div className="flex gap-4">
              <Icon size={24} color="black" />
            </div>
            <span className="text-sm text-gray-600">{name}</span>
          </div>
        ))}
    </div>
  );
};

export const AllIcons: Story = {
  render: () => <IconGrid />,
};
