import React, { useEffect, useState } from 'react';

interface SpinnerDotsProps {
  size?: number; // in pixels, default to 48
  dots?: number; // optional: allow changing number of dots
}

const LoadingSpinner: React.FC<SpinnerDotsProps> = ({ size = 58, dots = 10 }) => {
  const [tick, setTick] = useState(0);

  // Calculate center and radius based on size
  const CENTER = size / 2;
  const RADIUS = size / 2 - 6; // 6px padding for dot size

  useEffect(() => {
    const interval = setInterval(() => setTick((t) => t + 1), 100);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50">
      <div className="relative inline-block" style={{ width: size, height: size }}>
        {Array.from({ length: dots }).map((_, i) => {
          const angle = (i * 2 * Math.PI) / dots;
          const x = Math.round(CENTER + RADIUS * Math.cos(angle));
          const y = Math.round(CENTER + RADIUS * Math.sin(angle));
          const isActive = tick % dots === i;
          return (
            <span
              key={i}
              className={`absolute rounded-full transition-colors duration-200 ${
                isActive ? 'bg-gray-800' : 'bg-gray-300'
              }`}
              style={{
                width: size * 0.13, // dot size scales with spinner size
                height: size * 0.13,
                left: `${x}px`,
                top: `${y}px`,
                transform: 'translate(-50%, -50%)',
              }}
            />
          );
        })}
      </div>
    </div>
  );
};

export default LoadingSpinner;
